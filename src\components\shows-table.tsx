'use client';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { ShowInList } from '@/models/Show';
import ShowQuery from '@/services/queries/ShowQuery';
import { useQuery } from '@tanstack/react-query';
import { Archive, Check, Eye, Plus, XCircle } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

export default function ShowsTable() {
  const { data, isLoading } = useQuery({
    queryKey: ShowQuery.tags,
    queryFn: ShowQuery.getAll,
  });

  const columns = generateTableColumns<ShowInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Show Name', type: 'text', sortable: true },
      code: { name: 'Show Code', type: 'text', sortable: true },
      locationName: { name: 'Location', type: 'text', sortable: true },
      displayDate: {
        name: 'Display Date',
        type: {
          type: 'node',
          render: ({ cell }) => {
            try {
              return format(new Date(cell), 'MM/dd/yyyy');
            } catch {
              return cell || '-';
            }
          },
        },
        sortable: true,
      },
      startDate: {
        name: 'Start Date',
        type: {
          type: 'node',
          render: ({ cell }) => {
            try {
              return format(new Date(cell), 'MM/dd/yyyy');
            } catch {
              return cell || '-';
            }
          },
        },
        sortable: true,
      },
      endDate: {
        name: 'End Date',
        type: {
          type: 'node',
          render: ({ cell }) => {
            try {
              return format(new Date(cell), 'MM/dd/yyyy');
            } catch {
              return cell || '-';
            }
          },
        },
        sortable: true,
      },
      display: {
        name: 'Display',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <Check className="text-[#CDDB00] w-4 h-4 mr-1" />
                  <span className="text-[#CDDB00] font-medium text-xs">
                    Active
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="text-[#784311] w-4 h-4 mr-1" />
                  <span className="text-[#784311] font-medium text-xs">
                    Inactive
                  </span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
      view: {
        name: 'View',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <Check className="text-[#CDDB00] w-4 h-4 mr-1" />
                  <span className="text-[#CDDB00] font-medium text-xs">
                    Visible
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="text-[#784311] w-4 h-4 mr-1" />
                  <span className="text-[#784311] font-medium text-xs">
                    Hidden
                  </span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
      archive: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {!cell ? (
                <>
                  <Check className="text-[#CDDB00] w-4 h-4 mr-1" />
                  <span className="text-[#CDDB00] font-medium text-xs">
                    Active
                  </span>
                </>
              ) : (
                <>
                  <Archive className="text-[#784311] w-4 h-4 mr-1" />
                  <span className="text-[#784311] font-medium text-xs line-through">
                    Archived
                  </span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Link href={`/dashboard/setup/list-of-shows/${row.id}`}>
                <Button variant="secondary" size="icon">
                  <Eye className="size-4" />
                </Button>
              </Link>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowInList>({
    name: {
      name: 'Show Name',
      type: 'text',
    },
    code: {
      name: 'Show Code',
      type: 'text',
    },
    locationName: {
      name: 'Location',
      type: 'text',
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href="/dashboard/setup/list-of-shows/add">
            <Button variant="main">
              <Plus className="mr-2 h-4 w-4" />
              Add New Show
            </Button>
          </Link>
        </div>
      }
    />
  );
}

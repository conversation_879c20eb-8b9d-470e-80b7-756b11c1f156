'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Archive, Check, Eye, Filter, Plus, Search } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Show {
  name: string;
  code: string;
  location: string;
  displayDate: string;
  startDate: string;
  display: boolean;
  id: string;
}

const shows: Show[] = [
  {
    name: 'Agriculture Expo 2024',
    code: 'SH2408007',
    location: 'Quebec City Convention Centre',
    displayDate: '08/08/2024 - 08/10/2024',
    startDate: '2024-08-08',
    display: true,
    id: '1',
  },
  {
    name: 'Agriculture Expo 2024',
    code: 'SH2412023',
    location: 'Quebec City Convention Centre',
    displayDate: '12/24/2024 - 12/26/2024',
    startDate: '2024-12-24',
    display: true,
    id: '2',
  },
  {
    name: 'Agriculture Expo 2024',
    code: 'SH2408031',
    location: 'Quebec City Convention Centre',
    displayDate: '08/04/2024 - 08/06/2024',
    startDate: '2024-08-04',
    display: true,
    id: '3',
  },
  {
    name: 'Agriculture Expo 2024',
    code: 'SH2412047',
    location: 'Quebec City Convention Centre',
    displayDate: '12/20/2024 - 12/22/2024',
    startDate: '2024-12-20',
    display: false,
    id: '4',
  },
  {
    name: 'Agriculture Expo 2024',
    code: '*********',
    location: 'Quebec City Convention Centre',
    displayDate: '08/28/2024 - 08/30/2024',
    startDate: '2024-08-28',
    display: true,
    id: '5',
  },
  {
    name: 'Agriculture Expo 2024',
    code: '*********',
    location: 'Quebec City Convention Centre',
    displayDate: '12/16/2024 - 12/18/2024',
    startDate: '2024-12-16',
    display: true,
    id: '6',
  },
];

export default function ShowsTable() {
  const router = useRouter();

  const handleAddNewShow = () => {
    router.push('/dashboard/setup/add-show');
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-6">
        <div className="relative w-full md:w-80">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
          <Input type="text" placeholder="Search shows..." className="pl-8" />
        </div>

        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <Button
            className="bg-[#00646C] hover:bg-[#00646C]/90"
            onClick={handleAddNewShow}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add New Show
          </Button>
          <Button variant="outline" className="border-slate-200 text-slate-700">
            <Archive className="mr-2 h-4 w-4" />
            Archived Shows
          </Button>
          <Button variant="outline" className="border-slate-200 text-slate-700">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      <div className="text-sm text-slate-500 mb-4">
        Results: <span className="font-medium">419</span> shows found.
      </div>

      <div className="rounded-md border border-slate-200 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-medium">Show Name</TableHead>
              <TableHead className="font-medium">Show Code</TableHead>
              <TableHead className="font-medium">Show Location</TableHead>
              <TableHead className="font-medium">Display Date</TableHead>
              <TableHead className="font-medium">Start Date</TableHead>
              <TableHead className="font-medium">Display</TableHead>
              <TableHead className="font-medium">View</TableHead>
              <TableHead className="font-medium">Archive</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {shows.map((show, index) => (
              <TableRow
                key={show.id}
                className={index % 2 === 1 ? 'bg-slate-50' : ''}
              >
                <TableCell className="font-medium">{show.name}</TableCell>
                <TableCell>{show.code}</TableCell>
                <TableCell>{show.location}</TableCell>
                <TableCell>{show.displayDate}</TableCell>
                <TableCell>{show.startDate}</TableCell>
                <TableCell>
                  {show.display ? (
                    <span className="flex items-center">
                      <Check className="h-5 w-5 text-[#CED600] mr-1" />
                      <span className="text-[#CED600] font-medium">Active</span>
                    </span>
                  ) : (
                    <span className="text-[#77400F] font-medium">Inactive</span>
                  )}
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-[#00646C] hover:text-[#00646C]/80"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </TableCell>
                <TableCell>
                  <div className="flex items-center justify-center">
                    <div className="h-5 w-5 rounded-full border-2 border-slate-300"></div>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

'use client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import GroundConditionQuery from '@/services/queries/GroundConditionQuery';
import { GroundConditionCreate } from '@/models/GroundCondition';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { Spinner } from '@/components/ui/spinner';
import { GroundConditionCreateSchema } from '@/schema/GroundConditionSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { modal } from '@/components/ui/overlay';
import Field from '@/components/ui/inputs/field';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: GroundConditionCreate;
  id?: number;
}) {
  const queryClient = useQueryClient();
  const form = useForm<GroundConditionCreate>({
    resolver: zodResolver(GroundConditionCreateSchema),
    defaultValues: defaultValues || {
      name: '',
      text: '',
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (f: GroundConditionCreate) =>
      id ? GroundConditionQuery.update(id)(f) : GroundConditionQuery.create(f),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ground-conditions'] });
      toast({
        title: 'Success',
        description: id
          ? 'Ground condition updated'
          : 'Ground condition created',
        variant: 'success',
      });
      modal.close();
    },
    onError: (e: any) =>
      toast({ title: e.message || 'Failed to save', variant: 'destructive' }),
  });

  return (
    <Form {...form}>
      <ModalContainer
        title={id ? 'Update Ground Condition' : 'Add Ground Condition'}
        description={
          id ? 'Update ground condition details' : 'Create new ground condition'
        }
        onSubmit={form.handleSubmit((formData) => mutate(formData))}
        controls={
          <div className="flex justify-end w-full gap-4">
            <Button variant="main" disabled={isPending}>
              {id ? 'Update' : 'Add'}
            </Button>
          </div>
        }
      >
        <div className="flex flex-col gap-4 mt-4 pb-10 pr-10 h-[500px] overflow-y-auto scrollbar-hide ">
          <Field
            control={form.control}
            name="name"
            label="Name"
            required
            type="text"
          />
          <Field
            control={form.control}
            name="text"
            label="Description"
            required
            type="LightRichText"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

export default function GroundConditionModal({
  conditionId,
}: {
  conditionId?: number;
}) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['ground-conditions', conditionId],
    queryFn: () =>
      conditionId ? GroundConditionQuery.getById(conditionId) : undefined,
    enabled: !!conditionId,
  });

  let defaultValues: GroundConditionCreate | undefined = undefined;
  if (conditionId && data) {
    defaultValues = {
      name: data.name,
      text: data.text,
    };
  }

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent defaultValues={defaultValues} id={conditionId} />
      )}
    </Suspense>
  );
}

'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

const steps = [
  'Setup New Show xxxx',
  'Hall Details',
  'Promoter Info',
  'Show Schedule',
  'Dates & Times',
  'Options & Settings',
  'Booth Options',
  'Products',
  'Services',
  'Documents',
  'Review & Submit',
];

export default function AddShowPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    showName: '',
    showCode: '',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    displayDate: '',
    orderDeadlineDate: undefined as Date | undefined,
    lateChargePercentage: '35',
    kioskPrintingQueueDate: undefined as Date | undefined,
    showSite: '',
    showIsIn: '',
    showLink: 'http://www.example.com',
    showSiteDirection: '',
    showDescription: '',
    makeAvailable: true,
    showInventory: '',
    labourNonTaxable: false,
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (name: string, date: Date | undefined) => {
    setFormData((prev) => ({ ...prev, [name]: date }));
  };

  const handleCancel = () => {
    router.push('/');
  };

  const handleSaveAndContinue = () => {
    // In a real app, you would save the form data here
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Submit the form and redirect
      router.push('/');
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-slate-800 mb-2">Add New Show</h1>
        <p className="text-slate-600">
          Complete the form below to create a new show in the system. You can
          navigate between steps using the tabs.
        </p>
      </div>

      {/* Minimalistic tabs inspired by Dynamics 365 Business Central */}
      <div className="mb-8 border-b border-slate-200">
        <div className="flex overflow-x-auto">
          {steps.map((step, index) => (
            <button
              key={index}
              className={`px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors ${
                index <= currentStep
                  ? 'cursor-pointer'
                  : 'opacity-50 cursor-not-allowed'
              } ${
                index === currentStep
                  ? 'text-[#00646C] border-b-2 border-[#00646C]'
                  : 'text-slate-600 hover:text-slate-800'
              }`}
              onClick={() => index <= currentStep && setCurrentStep(index)}
              disabled={index > currentStep}
            >
              {step}
            </button>
          ))}
        </div>
      </div>

      <Card className="border-slate-200">
        <CardContent className="p-6">
          {currentStep === 0 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-slate-800">
                General Information xxxx
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="showName">
                    Show Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="showName"
                    name="showName"
                    value={formData.showName}
                    onChange={handleInputChange}
                    className="border-slate-200"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="showCode">
                    Show Code <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="showCode"
                    name="showCode"
                    placeholder="EXPO2025"
                    value={formData.showCode}
                    onChange={handleInputChange}
                    className="border-slate-200"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="startDate">Show Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal border-slate-200',
                          !formData.startDate && 'text-slate-500',
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.startDate
                          ? format(formData.startDate, 'PPP')
                          : 'Select date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.startDate}
                        onSelect={(date) => handleDateChange('startDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">Show End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal border-slate-200',
                          !formData.endDate && 'text-slate-500',
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.endDate
                          ? format(formData.endDate, 'PPP')
                          : 'Select date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.endDate}
                        onSelect={(date) => handleDateChange('endDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="displayDate">Show Display Date</Label>
                  <Input
                    id="displayDate"
                    name="displayDate"
                    placeholder="April 15–17, 2025"
                    value={formData.displayDate}
                    onChange={handleInputChange}
                    className="border-slate-200"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="orderDeadlineDate">
                    Order Deadline Date <span className="text-red-500">*</span>
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal border-slate-200',
                          !formData.orderDeadlineDate && 'text-slate-500',
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.orderDeadlineDate
                          ? format(formData.orderDeadlineDate, 'PPP')
                          : 'Select date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.orderDeadlineDate}
                        onSelect={(date) =>
                          handleDateChange('orderDeadlineDate', date)
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lateChargePercentage">
                    Late Charge Percentage
                  </Label>
                  <Input
                    id="lateChargePercentage"
                    name="lateChargePercentage"
                    type="number"
                    value={formData.lateChargePercentage}
                    onChange={handleInputChange}
                    className="border-slate-200"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="kioskPrintingQueueDate">
                    Kiosk Printing Queue Date
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal border-slate-200',
                          !formData.kioskPrintingQueueDate && 'text-slate-500',
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.kioskPrintingQueueDate
                          ? format(formData.kioskPrintingQueueDate, 'PPP')
                          : 'Select date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.kioskPrintingQueueDate}
                        onSelect={(date) =>
                          handleDateChange('kioskPrintingQueueDate', date)
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="showSite">
                    Show Site <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.showSite}
                    onValueChange={(value) =>
                      handleSelectChange('showSite', value)
                    }
                  >
                    <SelectTrigger className="border-slate-200">
                      <SelectValue placeholder="Select Show Site" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edmonton_expo">
                        Edmonton Expo Centre
                      </SelectItem>
                      <SelectItem value="calgary_bmo">
                        Calgary BMO Centre
                      </SelectItem>
                      <SelectItem value="vancouver_convention">
                        Vancouver Convention Centre
                      </SelectItem>
                      <SelectItem value="toronto_metro">
                        Toronto Metro Convention Centre
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="showIsIn">
                    Show is in <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.showIsIn}
                    onValueChange={(value) =>
                      handleSelectChange('showIsIn', value)
                    }
                  >
                    <SelectTrigger className="border-slate-200">
                      <SelectValue placeholder="Select Province" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="alberta">ALBERTA</SelectItem>
                      <SelectItem value="british_columbia">
                        BRITISH COLUMBIA
                      </SelectItem>
                      <SelectItem value="manitoba">MANITOBA</SelectItem>
                      <SelectItem value="new_brunswick">
                        NEW BRUNSWICK
                      </SelectItem>
                      <SelectItem value="newfoundland">NEWFOUNDLAND</SelectItem>
                      <SelectItem value="nova_scotia">NOVA SCOTIA</SelectItem>
                      <SelectItem value="ontario">ONTARIO</SelectItem>
                      <SelectItem value="pei">PRINCE EDWARD ISLAND</SelectItem>
                      <SelectItem value="quebec">QUEBEC</SelectItem>
                      <SelectItem value="saskatchewan">SASKATCHEWAN</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="showLink">Show Link</Label>
                  <Input
                    id="showLink"
                    name="showLink"
                    value={formData.showLink}
                    onChange={handleInputChange}
                    className="border-slate-200"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="showSiteDirection">Show Site Direction</Label>
                  <Input
                    id="showSiteDirection"
                    name="showSiteDirection"
                    value={formData.showSiteDirection}
                    onChange={handleInputChange}
                    className="border-slate-200"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="showDescription">Show Description</Label>
                <Textarea
                  id="showDescription"
                  name="showDescription"
                  value={formData.showDescription}
                  onChange={handleInputChange}
                  className="min-h-[100px] border-slate-200"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="makeAvailable"
                  checked={formData.makeAvailable}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange('makeAvailable', checked as boolean)
                  }
                />
                <Label htmlFor="makeAvailable" className="font-normal">
                  Make this show available to the users
                </Label>
              </div>

              <div className="border-t border-slate-200 pt-6">
                <h2 className="text-xl font-semibold text-slate-800 mb-4">
                  Inventory
                </h2>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="showInventory">Show Inventory</Label>
                    <Select
                      value={formData.showInventory}
                      onValueChange={(value) =>
                        handleSelectChange('showInventory', value)
                      }
                    >
                      <SelectTrigger className="border-slate-200">
                        <SelectValue placeholder="Select Inventory" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standard">
                          Standard Inventory
                        </SelectItem>
                        <SelectItem value="premium">
                          Premium Inventory
                        </SelectItem>
                        <SelectItem value="basic">Basic Inventory</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="labourNonTaxable"
                      checked={formData.labourNonTaxable}
                      onCheckedChange={(checked) =>
                        handleCheckboxChange(
                          'labourNonTaxable',
                          checked as boolean,
                        )
                      }
                    />
                    <Label htmlFor="labourNonTaxable" className="font-normal">
                      Labour is Non-Taxable
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Placeholder for other steps */}
          {currentStep > 0 && (
            <div className="min-h-[400px] flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-slate-800 mb-2">
                  {steps[currentStep]}
                </h2>
                <p className="text-slate-500">
                  This step is under development.
                </p>
              </div>
            </div>
          )}

          <div className="flex justify-between mt-8 pt-6 border-t border-slate-200">
            <Button
              variant="outline"
              className="border-slate-200"
              onClick={handleCancel}
            >
              Cancel
            </Button>

            <div className="flex gap-2">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  className="border-slate-200"
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
              )}
              <Button
                className="bg-[#00646C] hover:bg-[#00646C]/90"
                onClick={handleSaveAndContinue}
              >
                {currentStep < steps.length - 1 ? (
                  <>
                    Save & Continue
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

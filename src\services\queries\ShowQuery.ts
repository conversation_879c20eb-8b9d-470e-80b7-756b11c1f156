import {
  Show,
  ShowInList,
  ShowCreateRequest,
  ShowUpdateRequest,
} from '@/models/Show';
import fetcher from './fetcher';

const ShowQuery = {
  tags: ['Shows'] as const,

  getAll: async () => fetcher<ShowInList[]>('Shows'),

  getOne: async (id: number) => fetcher<Show>(`Shows/${id}/general-info`),

  create: async (data: ShowCreateRequest) =>
    fetcher<number>('Shows/general-info', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: ShowUpdateRequest) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'DELETE',
    }),

  toggleArchive: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/toggle-archive`, {
      method: 'PATCH',
    }),

  getHallContact: async (id: number) =>
    fetcher<{
      showId: number;
      hallId: number;
      contactId: number;
      hallName: string;
      hallCode: string;
      contactName: string;
      contactEmail: string;
      contactPhone: string;
    }>(`Shows/${id}/hall`),

  updateHallContact:
    (id: number) => async (data: { hallId: number; contactId: number }) =>
      fetcher<boolean>(`Shows/${id}/hall`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),
};

export default ShowQuery;

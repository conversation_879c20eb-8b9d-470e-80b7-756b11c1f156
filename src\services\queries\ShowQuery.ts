import {
  Show,
  ShowInList,
  ShowCreateRequest,
  ShowUpdateRequest,
} from '@/models/Show';
import fetcher from './fetcher';

const ShowQuery = {
  tags: ['Shows'] as const,

  getAll: async () => fetcher<ShowInList[]>('Shows'),

  getOne: async (id: number) => fetcher<Show>(`Shows/${id}/general-info`),

  create: async (data: ShowCreateRequest) =>
    fetcher<number>('Shows/general-info', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: ShowUpdateRequest) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'DELETE',
    }),
};

export default ShowQuery;

'use client';

import type React from 'react';

import { Bell, ChevronRight, Home, Menu, Search, Keyboard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSidebar } from '@/components/ui/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  CommandDialog,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
} from '@/components/ui/command';
import { useState } from 'react';
import { Badge } from './ui/Badge';

interface DashboardHeaderProps {
  showFactBox: boolean;
  setShowFactBox: (show: boolean) => void;
  currentRole: string;
  setCurrentRole: (role: string) => void;
  setShowKeyboardShortcuts: (show: boolean) => void;
}

export function DashboardHeader({
  showFactBox,
  setShowFactBox,
  currentRole,
  setCurrentRole,
  setShowKeyboardShortcuts,
}: DashboardHeaderProps) {
  const { toggleSidebar } = useSidebar();
  const [commandOpen, setCommandOpen] = useState(false);

  const roleNames = {
    finance: 'Finance Manager',
    sales: 'Sales Manager',
    inventory: 'Inventory Manager',
    admin: 'Administrator',
  };

  // Command palette search items
  const searchItems = [
    {
      group: 'Pages',
      items: [
        { name: 'Dashboard', shortcut: 'G D', action: () => {} },
        { name: 'Reports', shortcut: 'G R', action: () => {} },
        { name: 'Settings', shortcut: 'G S', action: () => {} },
      ],
    },
    {
      group: 'Actions',
      items: [
        { name: 'New Transaction', shortcut: 'N T', action: () => {} },
        { name: 'Export Data', shortcut: 'E D', action: () => {} },
        { name: 'Print Report', shortcut: 'P R', action: () => {} },
      ],
    },
    {
      group: 'Role Centers',
      items: [
        {
          name: 'Finance Role Center',
          shortcut: 'R F',
          action: () => setCurrentRole('finance'),
        },
        {
          name: 'Sales Role Center',
          shortcut: 'R S',
          action: () => setCurrentRole('sales'),
        },
        {
          name: 'Inventory Role Center',
          shortcut: 'R I',
          action: () => setCurrentRole('inventory'),
        },
        {
          name: 'Administrator Role Center',
          shortcut: 'R A',
          action: () => setCurrentRole('admin'),
        },
      ],
    },
  ];

  // Handle keyboard shortcut to open command palette
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      setCommandOpen(true);
    }
  };

  return (
    <TooltipProvider delayDuration={300}>
      <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b border-slate-200 bg-white px-4 shadow-sm sm:px-6">
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          onClick={toggleSidebar}
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>

        <div className="flex items-center gap-2 font-medium text-slate-700">
          <Home className="h-5 w-5" />
          <ChevronRight className="h-4 w-4 text-slate-400" />
          <span>
            {roleNames[currentRole as keyof typeof roleNames]} Role Center
          </span>
        </div>

        <div className="relative ml-auto flex-1 md:grow-0 md:w-80">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
          <Input
            type="search"
            placeholder="Search or type a command..."
            className="w-full pl-8 md:w-80 border-slate-200 focus-visible:ring-slate-300"
            onKeyDown={handleKeyDown}
            onClick={() => setCommandOpen(true)}
          />
          <kbd className="pointer-events-none absolute right-2 top-2 hidden h-5 select-none items-center gap-1 rounded border border-slate-200 bg-slate-50 px-1.5 font-mono text-xs font-medium opacity-100 sm:flex">
            <span className="text-xs">⌘</span>K
          </kbd>
        </div>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="relative"
              onClick={() => setShowKeyboardShortcuts(true)}
            >
              <Keyboard className="h-5 w-5 text-slate-600" />
              <span className="sr-only">Keyboard Shortcuts</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Keyboard Shortcuts (Shift+?)</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5 text-slate-600" />
              <Badge className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 flex items-center justify-center bg-rose-500">
                3
              </Badge>
              <span className="sr-only">Notifications</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>You have 3 unread notifications</p>
          </TooltipContent>
        </Tooltip>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-user.jpg" alt="User" />
                <AvatarFallback>GK</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setCurrentRole('finance')}>
              Finance Role Center
              <kbd className="ml-auto text-xs text-slate-400">R F</kbd>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentRole('sales')}>
              Sales Role Center
              <kbd className="ml-auto text-xs text-slate-400">R S</kbd>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentRole('inventory')}>
              Inventory Role Center
              <kbd className="ml-auto text-xs text-slate-400">R I</kbd>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentRole('admin')}>
              Administrator Role Center
              <kbd className="ml-auto text-xs text-slate-400">R A</kbd>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowFactBox(!showFactBox)}>
              {showFactBox ? 'Hide' : 'Show'} FactBox
              <kbd className="ml-auto text-xs text-slate-400">⌘B</kbd>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              Log out
              <kbd className="ml-auto text-xs text-slate-400">⌘Q</kbd>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </header>

      <CommandDialog open={commandOpen} onOpenChange={setCommandOpen}>
        <CommandInput placeholder="Type a command or search..." />
        <CommandList>
          {searchItems.map((group) => (
            <CommandGroup key={group.group} heading={group.group}>
              {group.items.map((item) => (
                <CommandItem
                  key={item.name}
                  onSelect={() => {
                    item.action();
                    setCommandOpen(false);
                  }}
                >
                  <span>{item.name}</span>
                  <kbd className="ml-auto text-xs text-slate-400">
                    {item.shortcut}
                  </kbd>
                </CommandItem>
              ))}
            </CommandGroup>
          ))}
        </CommandList>
      </CommandDialog>
    </TooltipProvider>
  );
}

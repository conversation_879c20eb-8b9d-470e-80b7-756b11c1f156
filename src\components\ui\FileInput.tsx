'use client';

import { Edit2, Trash2 as RemoveIcon } from 'lucide-react';
import {
  ComponentProps,
  Dispatch,
  SetStateAction,
  createContext,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  useDropzone,
  DropzoneState,
  FileRejection,
  DropzoneOptions,
} from 'react-dropzone';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { ImageCropperModal } from './image-cropper-modal';
import { Input } from './inputs/input';
import { DEFAULT_MODAL, modal } from './overlay';

type DirectionOptions = 'rtl' | 'ltr' | undefined;

type FileUploaderContextType = {
  dropzoneState: DropzoneState;
  isLOF: boolean;
  isFileTooBig: boolean;
  removeFileFromSet: (index: number) => void;
  getFileFromSet: (index: number) => File | undefined;
  replaceFileFromSet: (index: number, file: File) => void;
  activeIndex: number;
  setActiveIndex: Dispatch<SetStateAction<number>>;
  orientation: 'horizontal' | 'vertical';
  direction: DirectionOptions;
};

const FileUploaderContext = createContext<FileUploaderContextType | null>(null);

export const useFileUpload = () => {
  const context = useContext(FileUploaderContext);
  if (!context) {
    throw new Error('useFileUpload must be used within a FileUploaderProvider');
  }
  return context;
};

export type FileUploaderProps = {
  value: File[] | null;
  reSelect?: boolean;
  onValueChange: (value: File[] | null) => void;
  dropzoneOptions: DropzoneOptions;
  orientation?: 'horizontal' | 'vertical';
} & Pick<ComponentProps<typeof ImageCropperModal>, 'aspect'>;

export const FileUploader = forwardRef<
  HTMLDivElement,
  FileUploaderProps & React.HTMLAttributes<HTMLDivElement>
>(
  (
    {
      className,
      dropzoneOptions,
      value,
      onValueChange,
      reSelect,
      orientation = 'vertical',
      children,
      dir,
      aspect,
      ...props
    },
    ref,
  ) => {
    const [isFileTooBig, setIsFileTooBig] = useState(false);
    const [isLOF, setIsLOF] = useState(false);
    const [activeIndex, setActiveIndex] = useState(-1);
    const {
      accept = {
        'image/*': ['.jpg', '.jpeg', '.png', '.gif'],
      },
      maxFiles = 1,
      maxSize = 4 * 1024 * 1024,
      multiple = true,
    } = dropzoneOptions;

    const reSelectAll = maxFiles === 1 ? true : reSelect;
    const direction: DirectionOptions = dir === 'rtl' ? 'rtl' : 'ltr';

    const removeFileFromSet = useCallback(
      (i: number) => {
        if (!value) return;
        const newFiles = value.filter((_, index) => index !== i);
        onValueChange(newFiles);
      },
      [value, onValueChange],
    );
    const getFileFromSet = useCallback(
      (i: number) => {
        if (!value) return;
        return value[i];
      },
      [value],
    );

    const replaceFileFromSet = useCallback(
      (i: number, file: File) => {
        if (!value) return;
        const newFiles = [...value];
        if (file.type.startsWith('image')) {
          modal(
            ({ close }) => (
              <ImageCropperModal
                image={file}
                aspect={aspect}
                onSuccess={(f) => {
                  close();
                  newFiles[i] = f;
                  onValueChange(newFiles);
                }}
              />
            ),
            { ...DEFAULT_MODAL, height: '75%', width: '75%' },
          ).open({ previousBehavior: 'keep' });
        } else {
          newFiles[i] = file;
          onValueChange(newFiles);
        }
      },
      [value, aspect, onValueChange],
    );

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();

        if (!value) return;

        const moveNext = () => {
          const nextIndex = activeIndex + 1;
          setActiveIndex(nextIndex > value.length - 1 ? 0 : nextIndex);
        };

        const movePrev = () => {
          const nextIndex = activeIndex - 1;
          setActiveIndex(nextIndex < 0 ? value.length - 1 : nextIndex);
        };

        const prevKey =
          orientation === 'horizontal'
            ? direction === 'ltr'
              ? 'ArrowLeft'
              : 'ArrowRight'
            : 'ArrowUp';

        const nextKey =
          orientation === 'horizontal'
            ? direction === 'ltr'
              ? 'ArrowRight'
              : 'ArrowLeft'
            : 'ArrowDown';

        if (e.key === nextKey) {
          moveNext();
        } else if (e.key === prevKey) {
          movePrev();
        } else if (e.key === 'Enter' || e.key === 'Space') {
          if (activeIndex === -1) {
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            dropzoneState.inputRef.current?.click();
          }
        } else if (e.key === 'Delete' || e.key === 'Backspace') {
          if (activeIndex !== -1) {
            removeFileFromSet(activeIndex);
            if (value.length - 1 === 0) {
              setActiveIndex(-1);
              return;
            }
            movePrev();
          }
        } else if (e.key === 'Escape') {
          setActiveIndex(-1);
        }
      },
      [value, activeIndex, removeFileFromSet],
    );

    const onDrop = useCallback(
      (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
        const files = acceptedFiles;

        if (!files) {
          toast.error('file error , probably too big');
          return;
        }

        const newValues: File[] = value ? [...value] : [];

        if (reSelectAll) {
          newValues.splice(0, newValues.length);
        }
        if (files && files.length == 1 && files[0].type.startsWith('image')) {
          modal(
            ({ close }) => (
              <ImageCropperModal
                image={files[0]}
                aspect={aspect}
                onSuccess={(file) => {
                  close();
                  onValueChange([...newValues, file]);
                }}
              />
            ),
            { ...DEFAULT_MODAL, height: '80%', width: '75%' },
          ).open({ previousBehavior: 'keep' });
        } else {
          files.forEach((file) => {
            if (newValues.length < maxFiles) {
              newValues.push(file);
            }
          });
          onValueChange(newValues);
        }

        if (rejectedFiles.length > 0) {
          for (let i = 0; i < rejectedFiles.length; i++) {
            if (rejectedFiles[i].errors[0]?.code === 'file-too-large') {
              toast.error(
                `File is too large. Max size is ${maxSize / 1024 / 1024}MB`,
              );
              break;
            }
            if (rejectedFiles[i].errors[0]?.message) {
              toast.error(rejectedFiles[i].errors[0].message);
              break;
            }
          }
        }
      },
      [reSelectAll, value],
    );

    useEffect(() => {
      if (!value) return;
      if (value.length === maxFiles) {
        setIsLOF(true);
        return;
      }
      setIsLOF(false);
    }, [value, maxFiles]);

    const opts = dropzoneOptions
      ? dropzoneOptions
      : { accept, maxFiles, maxSize, multiple };

    const dropzoneState = useDropzone({
      ...opts,
      onDrop,
      onDropRejected: () => setIsFileTooBig(true),
      onDropAccepted: () => setIsFileTooBig(false),
    });

    return (
      <FileUploaderContext.Provider
        value={{
          dropzoneState,
          isLOF,
          isFileTooBig,
          removeFileFromSet,
          activeIndex,
          setActiveIndex,
          orientation,
          direction,
          getFileFromSet,
          replaceFileFromSet,
        }}
      >
        <div
          ref={ref}
          tabIndex={0}
          onKeyDownCapture={handleKeyDown}
          className={cn('grid w-full focus:outline-none  ', className, {
            'gap-2': value && value.length > 0,
          })}
          dir={dir}
          {...props}
        >
          {children}
        </div>
      </FileUploaderContext.Provider>
    );
  },
);

FileUploader.displayName = 'FileUploader';

export const FileUploaderContent = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ children, className, ...props }, ref) => {
  const { orientation } = useFileUpload();
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div
      className={cn('w-full ')}
      ref={containerRef}
      aria-description="content file holder"
    >
      <div
        {...props}
        ref={ref}
        className={cn(
          'flex rounded-xl gap-1',
          orientation === 'horizontal' ? 'flex-raw flex-wrap' : 'flex-col',
          className,
        )}
      >
        {children}
      </div>
    </div>
  );
});

FileUploaderContent.displayName = 'FileUploaderContent';

export const FileUploaderItem = forwardRef<
  HTMLDivElement,
  { index: number } & React.HTMLAttributes<HTMLDivElement>
>(({ className, index, children, ...props }, ref) => {
  const { removeFileFromSet, activeIndex, replaceFileFromSet, getFileFromSet } =
    useFileUpload();
  const isSelected = index === activeIndex;
  const file = useMemo(() => getFileFromSet(index), [index, getFileFromSet]);
  return (
    <div
      ref={ref}
      className={cn(
        ' flex   ',
        ' justify-between cursor-pointer overflow-hidden',
        className,
        isSelected ? 'bg-muted' : '',
      )}
      {...props}
    >
      <div className="font-medium leading-none tracking-tight bg-secondary flex flex-row justify-between items-center gap-1.5 h-full w-full">
        <div className="flex ">{children}</div>
        <div className="flex gap-1">
          {file?.type.startsWith('image') && (
            <button
              type="button"
              className={cn(
                'px-2 py-1 rounded-lg hover:bg-gray-100 shadow-none dark:hover:bg-muted  ',
              )}
              onClick={() => replaceFileFromSet(index, file)}
            >
              <span className="sr-only">Replace Image {index}</span>
              <Edit2 className="w-4 h-4 hover:stroke-destructive duration-200 ease-in-out" />
            </button>
          )}
          <button
            type="button"
            className={cn(
              'px-2 py-1 rounded-lg hover:bg-gray-100 shadow-none  dark:hover:bg-muted  ',
            )}
            onClick={() => removeFileFromSet(index)}
          >
            <span className="sr-only">remove item {index}</span>
            <RemoveIcon className="w-4 h-4 hover:stroke-destructive duration-200 ease-in-out" />
          </button>
        </div>
      </div>
    </div>
  );
});

FileUploaderItem.displayName = 'FileUploaderItem';

export const FileInput = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  const { dropzoneState, isFileTooBig, isLOF } = useFileUpload();
  const rootProps = isLOF ? {} : dropzoneState.getRootProps();
  return (
    <div
      ref={ref}
      {...props}
      className={`relative w-full ${
        isLOF ? 'opacity-50 cursor-not-allowed ' : 'cursor-pointer '
      }`}
    >
      <div
        className={cn(
          `w-full rounded-lg duration-300 ease-in-out
         ${
           dropzoneState.isDragAccept
             ? 'border-green-500'
             : dropzoneState.isDragReject || isFileTooBig
               ? 'border-red-500'
               : 'border-gray-300'
         }`,
          className,
        )}
        {...rootProps}
      >
        {children}
      </div>
      <Input
        ref={dropzoneState.inputRef}
        disabled={isLOF}
        {...dropzoneState.getInputProps()}
        className={`${isLOF ? 'cursor-not-allowed' : ''}`}
      />
    </div>
  );
});

FileInput.displayName = 'FileInput';

'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { getQueryClient } from '@/utils/query-client';
import Field from '@/components/ui/inputs/field';
import GroupQuery from '@/services/queries/GroupQuery';
import { GroupData, GroupSchema } from '@/schema/GroupSchema';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: GroupData;
  id?: number;
}) {
  const { toast } = useToast();
  const { data: groupType, isLoading: isLoadingGroupType } = useQuery({
    queryKey: GroupTypeQuery.tags,
    queryFn: GroupTypeQuery.getAll,
  });

  const form = useForm<GroupData>({
    resolver: zodResolver(GroupSchema),
    defaultValues: {
      name: defaultValues?.name ?? '',
      code: defaultValues?.code ?? '',
      groupTypeId: defaultValues?.groupTypeId?.toString() ?? '',
      isAvailable: defaultValues?.isAvailable ?? true,
    },
  });

  const { mutate } = useMutation({
    mutationFn: id ? GroupQuery.update(id) : GroupQuery.add,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: GroupQuery.tags,
      });
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Group Info', { id }],
        });
      }
      toast({
        title: 'Success',
        description: id ? 'Group updated successfully' : 'New group created',
        variant: 'success',
      });
      modal.close();
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        title={id ? 'Update Group' : 'Add Group'}
        description={id ? 'Update group details' : 'Create new group entry'}
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end w-full gap-4">
            <Button variant="main">{id ? 'Update' : 'Add'}</Button>
          </div>
        }
      >
        <div className="flex flex-col gap-2 mt-4">
          <Field
            control={form.control}
            name="name"
            label="Group Name"
            type="text"
            required
          />
          {id && (
            <Field
              control={form.control}
              name="code"
              label="Group Code"
              type="text"
              disabled
            />
          )}
          <Field
            control={form.control}
            name="groupTypeId"
            label="Group Type"
            type={{
              type: 'select',
              props: {
                placeholder: 'Select group type',
                options:
                  (groupType &&
                    groupType?.map((type) => ({
                      label: type.name,
                      value: type.id.toString(),
                    }))) ||
                  [],
              },
            }}
            required
          />
          <Field
            control={form.control}
            name="isAvailable"
            label="Available"
            type="checkbox"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

interface GroupModalProps {
  id?: number;
}

function GroupModal({ id }: GroupModalProps) {
  const {
    data: group,
    isPaused,
    isLoading,
  } = useQuery({
    queryKey: ['Group Info', { id }],
    queryFn: () => GroupQuery.get(Number(id!)),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent
          defaultValues={group}
          id={group ? Number(id) : undefined}
        />
      )}
    </Suspense>
  );
}

export default GroupModal;

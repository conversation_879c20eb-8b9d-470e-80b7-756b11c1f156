'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ShowQuery from '@/services/queries/ShowQuery';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const steps = [
  'Setup New Show',
  'Hall Details',
  'Promoter Info',
  'Show Schedule',
  'Dates & Times',
  'Options & Settings',
  'Booth Options',
  'Products',
  'Services',
  'Documents',
  'Review & Submit',
];

interface ShowFormTabsProps {
  showId?: number;
}

interface DaySchedule {
  id: string;
  date: Date | undefined;
  startTime: string;
  endTime: string;
  notes: string;
}

interface AddShowFormValues {
  showName: string;
  showCode: string;
  startDate?: Date;
  endDate?: Date;
  displayDate: string;
  orderDeadlineDate?: Date;
  lateChargePercentage: string;
  kioskPrintingQueueDate?: Date;
  showSite: string;
  showIsIn: string;
  showLink: string;
  showSiteDirection: string;
  showDescription: string;
  makeAvailable: boolean;
  showInventory: string;
  labourNonTaxable: boolean;
  hall: string;
  showSiteContact: string;
  aisleCarpeting: boolean;
  carpetColour: string;
  additionalRequirements: string;
  showManager: string;
  billedTo: string;
  showSubcontract: boolean;
  floorPlanRequired: boolean;
  taxOption: string;
  advanceMaterialWarehouse: string;
  scheduleComment: string;
  moveInDays: DaySchedule[];
  showDays: DaySchedule[];
  moveOutDays: DaySchedule[];
}

export default function ShowFormTabs({ showId }: ShowFormTabsProps) {
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const isEditMode = !!showId;
  const [currentStep, setCurrentStep] = useState(0);

  const { data: locations, isLoading: isLoadingLocations } = useQuery({
    queryKey: ShowLocationQuery.tags,
    queryFn: ShowLocationQuery.getAll,
  });

  const { data: showData, isLoading: isLoadingShow } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: isEditMode,
  });

  // Form for the first tab (real API data)
  const apiForm = useForm({
    defaultValues: {
      name: '',
      startDate: '',
      endDate: '',
      displayDate: '',
      orderDeadlineDate: '',
      lateChargePercentage: '0',
      link: '',
      description: '',
      display: true,
      locationId: '',
      kioskPrintingQueueDate: '',
      view: true,
    },
  });

  // Form for other tabs (placeholder data)
  const form = useForm<AddShowFormValues>({
    defaultValues: {
      showName: '',
      showCode: '',
      startDate: undefined,
      endDate: undefined,
      displayDate: '',
      orderDeadlineDate: undefined,
      lateChargePercentage: '35',
      kioskPrintingQueueDate: undefined,
      showSite: '',
      showIsIn: '',
      showLink: 'http://www.example.com',
      showSiteDirection: '',
      showDescription: '',
      makeAvailable: true,
      showInventory: '',
      labourNonTaxable: false,
      hall: '',
      showSiteContact: '',
      aisleCarpeting: false,
      carpetColour: '',
      additionalRequirements: '',
      showManager: '',
      billedTo: '',
      showSubcontract: false,
      floorPlanRequired: true,
      taxOption: 'gst',
      advanceMaterialWarehouse: '',
      scheduleComment: '',
      moveInDays: [
        {
          id: 'move-in-1',
          date: undefined,
          startTime: '08:00',
          endTime: '17:00',
          notes: '',
        },
      ],
      showDays: [
        {
          id: 'show-day-1',
          date: undefined,
          startTime: '09:00',
          endTime: '18:00',
          notes: '',
        },
      ],
      moveOutDays: [
        {
          id: 'move-out-1',
          date: undefined,
          startTime: '08:00',
          endTime: '17:00',
          notes: '',
        },
      ],
    },
  });

  // State for schedule management (for future tabs)
  // These will be used when implementing other tabs
  // const [scheduleItems, setScheduleItems] = useState<
  //   Array<{
  //     id: string;
  //     date: Date;
  //     type: string;
  //     startTime: string;
  //     endTime: string;
  //   }>
  // >([]);

  // const [moveInDays, setMoveInDays] = useState<DaySchedule[]>([
  //   {
  //     id: 'move-in-1',
  //     date: undefined,
  //     startTime: '08:00',
  //     endTime: '17:00',
  //     notes: '',
  //   },
  // ]);

  // const [showDays, setShowDays] = useState<DaySchedule[]>([
  //   {
  //     id: 'show-day-1',
  //     date: undefined,
  //     startTime: '09:00',
  //     endTime: '18:00',
  //     notes: '',
  //   },
  // ]);

  // const [moveOutDays, setMoveOutDays] = useState<DaySchedule[]>([
  //   {
  //     id: 'move-out-1',
  //     date: undefined,
  //     startTime: '08:00',
  //     endTime: '17:00',
  //     notes: '',
  //   },
  // ]);

  useEffect(() => {
    if (showData && isEditMode) {
      apiForm.reset({
        name: showData.name || '',
        startDate: showData.startDate ? showData.startDate.split('T')[0] : '',
        endDate: showData.endDate ? showData.endDate.split('T')[0] : '',
        displayDate: showData.displayDate
          ? showData.displayDate.split('T')[0]
          : '',
        orderDeadlineDate: showData.orderDeadlineDate
          ? showData.orderDeadlineDate.split('T')[0]
          : '',
        lateChargePercentage: showData.lateChargePercentage || '0',
        link: showData.link || '',
        description: showData.description || '',
        display: showData.display,
        locationId: showData.locationId?.toString() || '',
        kioskPrintingQueueDate: showData.kioskPrintingQueueDate
          ? showData.kioskPrintingQueueDate.split('T')[0]
          : '',
        view: showData.view,
      });
    }
  }, [showData, isEditMode, apiForm]);

  const createMutation = useMutation({
    mutationFn: ShowQuery.create,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Show created successfully',
      });
      queryClient.invalidateQueries({ queryKey: ShowQuery.tags });
      router.push('/dashboard/setup/list-of-shows');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create show',
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: ShowQuery.update(showId!),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Show updated successfully',
      });
      queryClient.invalidateQueries({ queryKey: ShowQuery.tags });
      queryClient.invalidateQueries({ queryKey: ['Shows', showId] });
      router.push('/dashboard/setup/list-of-shows');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update show',
        variant: 'destructive',
      });
    },
  });

  const onSubmitFirstTab = (data: any) => {
    const formattedData = {
      ...data,
      locationId: Number(data.locationId),
      startDate: data.startDate + 'T00:00:00',
      endDate: data.endDate + 'T23:59:59',
      displayDate: data.displayDate + 'T00:00:00',
      orderDeadlineDate: data.orderDeadlineDate + 'T23:59:59',
      kioskPrintingQueueDate: data.kioskPrintingQueueDate
        ? data.kioskPrintingQueueDate + 'T00:00:00'
        : '',
      link: data.link || '',
      description: data.description || '',
    };

    if (isEditMode) {
      updateMutation.mutate(formattedData);
    } else {
      createMutation.mutate(formattedData);
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/setup/list-of-shows');
  };

  const handleSaveAndContinue = () => {
    if (currentStep === 0) {
      // For the first tab, submit the real API data
      apiForm.handleSubmit(onSubmitFirstTab)();
    } else {
      // For other tabs, just navigate (placeholder functionality)
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        router.push('/dashboard/setup/list-of-shows');
      }
    }
  };

  if (isLoadingShow || isLoadingLocations) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  const locationOptions =
    locations?.map((location) => ({
      label: location.name || 'Unknown Location',
      value: location.id.toString(),
    })) || [];

  return (
    <div>
      <div className="mb-4">
        <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
          {isEditMode ? 'EDIT SHOW' : 'SETUP NEW SHOW'}
        </h1>
        <p className="text-slate-600 text-sm">
          Please complete the show's information. Required information is marked
          with a <span className="text-red-500">*</span>.
          <br />
          Form Buttons are located at the Bottom of the Form.
        </p>
      </div>

      <div className="flex">
        {/* Vertical tabs */}
        <div className="w-48 shrink-0 border-r border-slate-200 pr-4 mr-6">
          <div className="space-y-1">
            {steps.map((step, index) => (
              <button
                key={index}
                className={`w-full text-left px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  index === currentStep
                    ? 'bg-[#00646C]/10 text-[#00646C] border-l-2 border-[#00646C]'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
                onClick={() => setCurrentStep(index)}
              >
                {step}
              </button>
            ))}
          </div>
        </div>

        {/* Form content */}
        <div className="flex-1">
          {currentStep === 0 ? (
            <Form {...apiForm}>
              <form onSubmit={apiForm.handleSubmit(onSubmitFirstTab)}>
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      General Information
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Field
                        control={apiForm.control}
                        name="name"
                        label="Show Name"
                        type="text"
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="locationId"
                        label="Location"
                        type={{
                          type: 'select',
                          props: {
                            options: locationOptions,
                            placeholder: 'Select Location',
                          },
                        }}
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="startDate"
                        label="Start Date"
                        type="date"
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="endDate"
                        label="End Date"
                        type="date"
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="displayDate"
                        label="Display Date"
                        type="date"
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="orderDeadlineDate"
                        label="Order Deadline Date"
                        type="date"
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="lateChargePercentage"
                        label="Late Charge Percentage"
                        type="number"
                        required
                      />

                      <Field
                        control={apiForm.control}
                        name="kioskPrintingQueueDate"
                        label="Kiosk Printing Queue Date"
                        type="date"
                      />

                      <div className="md:col-span-2">
                        <Field
                          control={apiForm.control}
                          name="link"
                          label="Show Link"
                          type="text"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <Field
                          control={apiForm.control}
                          name="description"
                          label="Description"
                          type="textarea"
                        />
                      </div>

                      <Field
                        control={apiForm.control}
                        name="display"
                        label="Display Show"
                        type="checkbox"
                      />

                      <Field
                        control={apiForm.control}
                        name="view"
                        label="Show Visible"
                        type="checkbox"
                      />
                    </div>
                  </div>
                </div>
              </form>
            </Form>
          ) : (
            <FormProvider {...form}>
              <form onSubmit={form.handleSubmit(handleSaveAndContinue)}>
                {/* Placeholder content for other tabs */}
                <div className="min-h-[400px] flex items-center justify-center">
                  <div className="text-center">
                    <h2 className="text-xl font-semibold text-slate-800 mb-2">
                      {steps[currentStep]}
                    </h2>
                    <p className="text-slate-500">
                      This step is under development.
                    </p>
                  </div>
                </div>
              </form>
            </FormProvider>
          )}

          <div className="flex justify-between mt-8 pt-6 border-t border-slate-200">
            <Button
              variant="outline"
              className="border-slate-200"
              onClick={handleCancel}
            >
              Cancel
            </Button>

            <div className="flex gap-2">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  className="border-slate-200"
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
              )}
              <Button
                className="bg-[#00646C] hover:bg-[#00646C]/90"
                onClick={handleSaveAndContinue}
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {createMutation.isPending || updateMutation.isPending ? (
                  <Spinner className="mr-2 h-4 w-4" />
                ) : null}
                {currentStep < steps.length - 1 ? (
                  <>
                    Save & Continue
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

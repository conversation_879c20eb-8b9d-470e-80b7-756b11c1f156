'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import PropertyQuery from '@/services/queries/PropertyQuery';
import { Property } from '@/models/Property';

export const PropertyTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: PropertyQuery.tags,
    queryFn: PropertyQuery.getAll,
  });

  const columns = generateTableColumns<Property>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      description: { name: 'Description', type: 'text', sortable: false },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Link
                href={`/dashboard/setup/master-setup/property/${row.id ?? 'add'}`}
              >
                <Button variant="secondary" size="icon">
                  <Edit2 className="size-4" />
                </Button>
              </Link>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Property>({
    name: { name: 'Name', type: 'text' },
    code: { name: 'Code', type: 'text' },
    description: { name: 'Description', type: 'text' },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href={`/dashboard/setup/master-setup/property/add`}>
            <Button variant="main">
              <FaPlus />
              Add New Property
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default PropertyTable;

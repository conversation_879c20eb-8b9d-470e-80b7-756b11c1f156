import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';

interface DeleteImageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  loading?: boolean;
}

const DeleteImageDialog: React.FC<DeleteImageDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
  loading,
}) => (
  <AlertDialog open={open} onOpenChange={onOpenChange}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Delete Image</AlertDialogTitle>
      </AlertDialogHeader>
      <div className="py-2 text-slate-700">
        Are you sure you want to delete this image? This action cannot be
        undone.
      </div>
      <AlertDialogFooter>
        <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
        <AlertDialogAction
          onClick={onConfirm}
          disabled={loading}
          className="bg-red-600 hover:bg-red-700"
        >
          {loading ? 'Deleting...' : 'Delete'}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

export default DeleteImageDialog;

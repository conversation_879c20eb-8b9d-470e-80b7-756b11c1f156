import type { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import AddShowForm from '@/components/add-show-form';

export const metadata: Metadata = {
  title: 'Add New Show | GOODKEY SHOW SERVICES LTD.',
  description:
    'Create a new show in the GOODKEY SHOW SERVICES LTD. system with comprehensive details and scheduling.',
  openGraph: {
    title: 'Add New Show | GOODKEY SHOW SERVICES LTD.',
    description:
      'Create a new show in the GOODKEY SHOW SERVICES LTD. system with comprehensive details and scheduling.',
  },
};

export default function AddShowPage() {
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Add New Show', link: '/dashboard/setup/add-show' },
      ]}
    >
      <AddShowForm />
    </AppLayout>
  );
}

'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>Off, User, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

interface ContactCredentialsProps {
  username: string;
  password: string;
}

export default function ContactCredentials({
  username,
  password,
}: ContactCredentialsProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(true);
  const { toast } = useToast();

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      toast({
        title: 'Copied!',
        description: `${fieldName} copied to clipboard`,
      });

      setTimeout(() => {
        setCopiedField(null);
      }, 2000);
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Failed to copy',
        description: 'Could not copy to clipboard',
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-[#00646C] flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          Login Credentials
        </CardTitle>
        <p className="text-sm text-slate-600">
          User account has been automatically created for this contact. These
          credentials can be used to access the system.
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-4">
          <div className="grid grid-cols-[150px_1fr] items-center gap-2">
            <Label
              htmlFor="username"
              className="text-sm font-medium text-slate-700 flex items-center gap-2"
            >
              <User className="h-4 w-4 text-slate-500" />
              Username
            </Label>
            <div className="flex items-center gap-2">
              <Input
                id="username"
                value={username}
                readOnly
                className="bg-slate-50 border-slate-300 text-slate-800 cursor-default text-sm"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(username, 'Username')}
                className="flex items-center gap-1 px-3 h-10"
              >
                {copiedField === 'Username' ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-[150px_1fr] items-center gap-2">
            <Label
              htmlFor="password"
              className="text-sm font-medium text-slate-700 flex items-center gap-2"
            >
              <Lock className="h-4 w-4 text-slate-500" />
              Password
            </Label>
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  readOnly
                  className="bg-slate-50 border-slate-300 text-slate-800 cursor-default pr-10 text-sm"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-slate-100"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-slate-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-slate-500" />
                  )}
                </Button>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(password, 'Password')}
                className="flex items-center gap-1 px-3 h-10"
              >
                {copiedField === 'Password' ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>

        <div className="bg-slate-50 border border-slate-200 rounded-md p-4 mt-4">
          <p className="text-sm text-slate-700">
            <strong className="text-slate-800">Note:</strong> All contact
            accounts are created with the default password "blue". Users should
            change their password after first login for security.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

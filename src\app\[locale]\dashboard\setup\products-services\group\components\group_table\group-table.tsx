'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, Edit2, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import GroupQuery from '@/services/queries/GroupQuery';
import { Button } from '@/components/ui/button';
import GroupModal from '../group_modal';
import { GroupDto } from '@/models/Category';

export const GroupTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: GroupQuery.tags,
    queryFn: GroupQuery.getAll,
  });

  const columns = generateTableColumns<GroupDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      groupTypeName: { name: 'Group Type', type: 'text', sortable: true },
      isAvailable: {
        name: 'Available',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Yes</span>
                </>
              ) : (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">No</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full justify-center">
              <Button
                variant="secondary"
                size="icon"
                onClick={() => {
                  modal(<GroupModal id={row.id} />, DEFAULT_MODAL).open();
                }}
              >
                <Edit2 className="size-4" />
              </Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<GroupDto>({
    name: {
      name: 'Name',
      type: 'text',
    },
    groupTypeName: { name: 'Group Type', type: 'text' },
    isAvailable: {
      name: 'Available',
      type: {
        type: 'select',
        options: [
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <DataTable
      filterFields={filters}
      columns={columns}
      data={data}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Button
            variant="main"
            onClick={() => {
              modal(<GroupModal />, {
                ...DEFAULT_MODAL,
                width: '40%',
              }).open();
            }}
          >
            <FaPlus />
            Add New Group
          </Button>
        </div>
      }
    />
  );
};

export default GroupTable;

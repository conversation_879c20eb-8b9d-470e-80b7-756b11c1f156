'use client';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import Suspense from '@/components/ui/Suspense';
import { getMenuName, MenuConfig } from '@/models/MenuItem';
import { MenuItemData, MenuItemSchema } from '@/schema/MenuItemSchema';
import MenuQuery from '@/services/queries/MenuQuery';
import PermissionQuery from '@/services/queries/PermissionQuery';
import RoleQuery from '@/services/queries/RoleQuery';
import CardGenerator from '@/components/ui/card_generator';
interface IMenuForm {
  data?: MenuItemData;
  id?: number;
  isDashboard: boolean;
}
export default function MenuForm({ data, id, isDashboard }: IMenuForm) {
  const { toast } = useToast();
  const { push } = useRouter();
  const { mutate, isPending } = useMutation({
    mutationFn: id ? MenuQuery.update(id) : MenuQuery.create,
    onSuccess: async (d: number | boolean) => {
      await getQueryClient().invalidateQueries({
        queryKey: [...MenuQuery.tags],
      });
      await getQueryClient().invalidateQueries({
        queryKey: [...MenuQuery.tags, { id }],
      });
      await getQueryClient().invalidateQueries({
        queryKey: [MenuQuery.tags, 'brief'],
      });

      toast({
        variant: 'default',
        title: id ? 'Updated Successfully' : 'Created Successfully',
      });
      if (typeof d == 'number') {
        if (isDashboard)
          push(`/dashboard/setup/system-setup/menu-management/${d}`);
        else push(`/dashboard/cms/navigation/${d}`);
      }
    },
  });
  const {
    isLoading,
    isError,
    data: menuItems,
  } = useQuery({
    queryKey: [...MenuQuery.tags],
    queryFn: () => MenuQuery.getAll(),
  });
  const { data: menus } = useQuery({
    queryKey: [...MenuQuery.tags, 'menu'],
    queryFn: () => MenuQuery.getSections(),
  });

  console.log('menus', menus);
  const form = useForm<MenuItemData>({
    resolver: zodResolver(MenuItemSchema),
    defaultValues: data
      ? {
          displayOrder: data.displayOrder?.toString(),
          iconName: data.iconName,
          isVisible: data.isVisible,
          url: data.url,
          parentId: data.parentId?.toString(),
          permissionKey: data.permissionKey,
          target: data.target,
          image: data.image,
          description: data.description,
          name: data.name,
          metaDescription: data.metaDescription,
          keywords: data.keywords,
          roleId: data.roleId?.toString(),
          level: data.level?.toString(),
          menuIds: data.menuIds?.map((i) => i.toString()),
          isStatic: data.isStatic,
          direction: data.direction?.toString(),
          isParent: data.isParent,
          isDashboard: data.isDashboard,
          sectionId: data.sectionId?.toString(),
        }
      : {
          description: '',
          name: '',
          metaDescription: '',
          keywords: '',
          isVisible: true,
          menuIds: [],
          isStatic: false,
          isParent: false,
          isDashboard: isDashboard,
          target: '_self',
        },
  });
  const { data: permissions } = useQuery({
    queryKey: PermissionQuery.tags,
    queryFn: PermissionQuery.getAll,
  });
  const { data: roles } = useQuery({
    queryKey: RoleQuery.tags,
    queryFn: RoleQuery.getAll,
  });

  const config: MenuConfig = {
    hasDescription: !isDashboard,
    hasImage: !isDashboard,
    isRoleBased: false,
    isPermissionBased: false,
    isLevelBased: false,
    hasIcon: isDashboard,
    id: 0,
    name: '',
  };
  return (
    <Suspense isLoading={isLoading}>
      <Form {...form}>
        <form
          className="flex flex-col gap-6 w-full rounded-[12px]  max-w-2xl"
          onSubmit={form.handleSubmit((d) => mutate(d))}
        >
          <div className="grid grid-cols-1 gap-6">
            <Field
              control={form.control}
              name="isParent"
              label="Parent"
              type="checkbox"
              // containerClassName="flex flex-row-reverse gap-4 w-fit items-center"
            />
            <Field
              control={form.control}
              name="parentId"
              label="Parent Menu"
              type={{
                type: 'select',
                props: {
                  withNone: true,
                  options: [
                    ...(menuItems
                      ?.filter((c) => c.isParent && c.id != id)
                      .filter((c) => c.isDashboard == isDashboard)
                      .map((item) => ({
                        label: getMenuName(item),
                        value: item.id?.toString() ?? '',
                      })) ?? []),
                  ],
                  placeholder: 'None',
                },
              }}
            />

            <div className=" grid grid-cols-1  gap-4">
              <Field
                control={form.control}
                name="name"
                label="Name"
                required={true}
                type="text"
              />
              <Field
                control={form.control}
                name="url"
                label="URL"
                required={true}
                type="text"
              />
              {config.hasDescription && (
                <Field
                  control={form.control}
                  name="description"
                  label="Description"
                  required={false}
                  type="text"
                />
              )}
            </div>
            <Field
              control={form.control}
              name="target"
              label="Target"
              containerClassName=""
              type={{
                type: 'select',
                props: {
                  options: [
                    { label: 'Same tab', value: '_self' },
                    { label: 'New tab', value: '_blank' },
                  ],
                  placeholder: 'Target',
                },
              }}
            />
            <Field
              control={form.control}
              // containerClassName=" flex gap-2 justify-center"
              name="sectionId"
              label="Sections"
              type={{
                type: 'select',
                props: {
                  options:
                    menus?.map((item) => ({
                      label: item.name,
                      value: item.id.toString(),
                    })) ?? [],
                  placeholder: 'None',
                },
              }}
            />
            {config.hasIcon && (
              <Field
                control={form.control}
                name="iconName"
                label="Icon Name"
                type={'text'}
              />
            )}
            {config.isPermissionBased && (
              <Field
                control={form.control}
                name="permissionKey"
                label="Required Permission (optional)"
                type={{
                  type: 'select',
                  props: {
                    options:
                      permissions?.map((item) => ({
                        label: item.code,
                        value: item.code,
                      })) ?? [],
                    placeholder: 'None',
                  },
                }}
              />
            )}
            {config.isRoleBased && (
              <Field
                control={form.control}
                name="roleId"
                label="Required Role (optional)"
                type={{
                  type: 'select',
                  props: {
                    options:
                      roles?.map((item) => ({
                        label: item.name,
                        value: item.id.toString(),
                      })) ?? [],
                    placeholder: 'None',
                  },
                }}
              />
            )}
            {config.isLevelBased && (
              <Field
                control={form.control}
                name="level"
                label="Required Level (optional)"
                type={'number'}
              />
            )}
            <Field
              control={form.control}
              name="displayOrder"
              label="Display order"
              type={'number'}
            />
            <Field
              control={form.control}
              name="direction"
              label="Direction"
              type={{
                type: 'select',
                props: {
                  withNone: true,
                  options: [
                    { label: 'Left to Right ', value: 'ltr' },
                    { label: 'Top to Bottom', value: 'ttb' },
                  ],
                  placeholder: 'None',
                },
              }}
            />
            <Field
              control={form.control}
              name="isVisible"
              label="Visible"
              type="checkbox"
            />
            <Field
              control={form.control}
              name="isStatic"
              label="Static"
              type="checkbox"
            />
            {config.hasImage && (
              <Field
                control={form.control}
                label="Main Image"
                name={`image`}
                // label="Main Image"
                containerClassName=" w-full"
                required
                type={{
                  type: 'file',
                  props: {
                    dropzoneOptions: {
                      accept: {
                        'image/*': ['.jpg', '.jpeg', '.png'],
                      },
                      multiple: false,
                      maxFiles: 1,
                    },
                  },
                }}
              />
            )}
            {config.hasDescription && (
              <CardGenerator title="SEO" className="">
                <div className="grid grid-cols-1 gap-6">
                  <Field
                    control={form.control}
                    name="keywords"
                    label="Keywords"
                    required={true}
                    type="tags"
                  />
                  <Field
                    control={form.control}
                    name="metaDescription"
                    label="Meta description"
                    required={false}
                    type="text"
                  />
                </div>
              </CardGenerator>
            )}
          </div>

          <div className="flex justify-end items-center gap-4">
            <Button variant="default" type="submit" disabled={isPending}>
              {isPending
                ? 'Saving...'
                : id
                  ? 'Update Menu Item'
                  : 'Create Menu Item'}
            </Button>
          </div>
        </form>
      </Form>
    </Suspense>
  );
}

import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import ProductSection from './component/product_section';

export default async function Layout({
  children,
  params,
}: Readonly<{
  children: ReactNode;
  params: Promise<{
    groupId: string;
    categoryId: string;
    id: string;
  }>;
}>) {
  const { groupId, categoryId, id } = await params;

  const isEdit = !Number.isNaN(Number(id)) && id !== 'add';

  if (!isEdit && id !== 'add') {
    redirect(
      `/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/add`,
    );
  }

  return (
    <div className="flex flex-col self-stretch w-full gap-8">
      <HydrationBoundary state={dehydrate(getQueryClient())}>
        <AppLayout
          items={[
            { title: 'Setup', link: '/dashboard/setup' },
            {
              title: 'Products & Services',
              link: '/dashboard/setup',
            },
            {
              title: 'Product',
              link: '/dashboard/setup/products-services/product',
            },
            {
              title: 'Product Detail',
              link: `/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}`,
            },
          ]}
        >
          <div className="flex w-full">
            <ProductSection
              id={isEdit ? Number(id) : undefined}
              groupId={Number(groupId)}
              categoryId={Number(categoryId)}
            />
            <div className="w-full overflow-y-auto scrollbar-hide px-1">
              {children}
            </div>
          </div>
        </AppLayout>
      </HydrationBoundary>
    </div>
  );
}

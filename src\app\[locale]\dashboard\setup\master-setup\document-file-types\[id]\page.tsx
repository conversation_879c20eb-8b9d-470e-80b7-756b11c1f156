import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { getQueryClient } from '@/utils/query-client';
import DocumentFileTypeForm from './components/document_file_type_form/document-file-type-form';
import AppLayout from '@/components/ui/app_layout';
import CardGenerator from '@/components/ui/card_generator';

export default async function DocumentFileTypePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    const isAdd = id === 'add';

    const client = getQueryClient();

    if (!isAdd) {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: [...DocumentFileTypeQuery.tags, { id: Number(id) }],
        queryFn: () => DocumentFileTypeQuery.getOne(Number(id)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
      {
        title: 'Document File Types',
        link: '/dashboard/setup/master-setup/document-file-types',
      },
      {
        title: isAdd ? 'Add File Type' : 'Edit File Type',
        link: `/dashboard/setup/master-setup/document-file-types/${id}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <CardGenerator
            title={
              isAdd ? 'Add New Document File Type' : 'Edit Document File Type'
            }
            description={
              isAdd
                ? 'Create a new document file type'
                : 'Update document file type information'
            }
          >
            <DocumentFileTypeForm id={isAdd ? undefined : Number(id)} />
          </CardGenerator>
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/document-file-types/add');
  }
}

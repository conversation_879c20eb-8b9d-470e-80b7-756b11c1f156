import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import CategorySection from './components/category_section';

export default async function Layout({
  children,
  params,
}: Readonly<{
  children: ReactNode;
  params: Promise<{ id: string }>;
}>) {
  const resolvedParams = await params;
  const { id } = resolvedParams;

  const isEdit = !Number.isNaN(Number(id)) && id !== 'add';

  if (!isEdit && id !== 'add') {
    redirect('/dashboard/setup/products-services/category/add');
  }

  return (
    <div className="flex flex-col self-stretch w-full gap-8">
      <HydrationBoundary state={dehydrate(getQueryClient())}>
        <AppLayout
          items={[
            { title: 'Setup', link: '/dashboard/setup' },
            {
              title: 'Products & Services',
              link: '/dashboard/setup',
            },
            {
              title: 'Category',
              link: '/dashboard/setup/products-services/category',
            },
            {
              title: 'Category Detail',
              link: `/dashboard/setup/products-services/category/${id}`,
            },
          ]}
        >
          <div className="flex w-full">
            <CategorySection id={isEdit ? Number(id) : undefined} />
            <div className="w-full overflow-y-auto scrollbar-hide px-1">
              {children}
            </div>
          </div>
        </AppLayout>
      </HydrationBoundary>
    </div>
  );
}

export interface ShowInList {
  id: number;
  name: string;
  code: string;
  location: string;
  displayDate: string;
  startDate: string;
  endDate: string;
  display: boolean;
  isArchived: boolean;
}

export interface Show {
  id: number;
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate?: string;
  lateChargePercentage: number;
  kioskPrintingQueueDate?: string;
  showSite: string;
  showIsIn: string;
  showLink?: string;
  showSiteDirection?: string;
  showDescription?: string;
  makeAvailable: boolean;
  showInventory?: string;
  labourNonTaxable: boolean;
  hall: string;
  showSiteContact?: string;
  aisleCarpeting: boolean;
  carpetColour?: string;
  additionalRequirements?: string;
  showManager: string;
  billedTo: string;
  showSubcontract: boolean;
  floorPlanRequired: boolean;
  taxOption: string;
  advanceMaterialWarehouse?: string;
  scheduleComment?: string;
  display: boolean;
  isArchived: boolean;
}

export interface ShowCreateRequest {
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate?: string;
  lateChargePercentage: number;
  kioskPrintingQueueDate?: string;
  showSite: string;
  showIsIn: string;
  showLink?: string;
  showSiteDirection?: string;
  showDescription?: string;
  makeAvailable: boolean;
  showInventory?: string;
  labourNonTaxable: boolean;
  hall: string;
  showSiteContact?: string;
  aisleCarpeting: boolean;
  carpetColour?: string;
  additionalRequirements?: string;
  showManager: string;
  billedTo: string;
  showSubcontract: boolean;
  floorPlanRequired: boolean;
  taxOption: string;
  advanceMaterialWarehouse?: string;
  scheduleComment?: string;
  display?: boolean;
  isArchived?: boolean;
}

export interface ShowUpdateRequest {
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate?: string;
  lateChargePercentage: number;
  kioskPrintingQueueDate?: string;
  showSite: string;
  showIsIn: string;
  showLink?: string;
  showSiteDirection?: string;
  showDescription?: string;
  makeAvailable: boolean;
  showInventory?: string;
  labourNonTaxable: boolean;
  hall: string;
  showSiteContact?: string;
  aisleCarpeting: boolean;
  carpetColour?: string;
  additionalRequirements?: string;
  showManager: string;
  billedTo: string;
  showSubcontract: boolean;
  floorPlanRequired: boolean;
  taxOption: string;
  advanceMaterialWarehouse?: string;
  scheduleComment?: string;
  display: boolean;
  isArchived: boolean;
}

export interface ShowScheduleDay {
  id?: string;
  date: string;
  startTime: string;
  endTime: string;
  notes?: string;
  type: 'moveIn' | 'show' | 'moveOut';
}

export interface ShowSchedule {
  showId: number;
  moveInDays: ShowScheduleDay[];
  showDays: ShowScheduleDay[];
  moveOutDays: ShowScheduleDay[];
}

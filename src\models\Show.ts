export interface ShowInList {
  id: number;
  archive: boolean;
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link: string;
  description: string;
  display: boolean;
  createdAt: string;
  createdBy: number;
  locationId: number;
  kioskPrintingQueueDate: string;
  view: boolean;
  createdByUsername: string;
  locationName: string;
}

export interface Show {
  id: number;
  archive: boolean;
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link: string;
  description: string;
  display: boolean;
  createdAt: string;
  createdBy: number;
  locationId: number;
  kioskPrintingQueueDate: string;
  view: boolean;
  createdByUsername: string;
  locationName: string;
}

export interface ShowCreateRequest {
  name: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link?: string;
  description?: string;
  display: boolean;
  locationId: number;
  kioskPrintingQueueDate?: string;
  view: boolean;
}

export interface ShowUpdateRequest {
  name: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link?: string;
  description?: string;
  display: boolean;
  locationId: number;
  kioskPrintingQueueDate?: string;
  view: boolean;
}

export interface ShowScheduleDay {
  id?: string;
  date: string;
  startTime: string;
  endTime: string;
  notes?: string;
  type: 'moveIn' | 'show' | 'moveOut';
}

export interface ShowSchedule {
  showId: number;
  moveInDays: ShowScheduleDay[];
  showDays: ShowScheduleDay[];
  moveOutDays: ShowScheduleDay[];
}

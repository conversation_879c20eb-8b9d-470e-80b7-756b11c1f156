'use client';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import GroundServiceQuery from '@/services/queries/GroundServiceQuery';
import { GroundService } from '@/models/GroundService';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import GroundServiceModal from './GroundServiceModal';
import { useMemo, useState } from 'react';
import { Edit2, Trash2, Plus, CheckCircle, XCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';

export default function GroundServicesManagementSection() {
  const router = useRouter();
  const { toast } = useToast();

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    id?: number;
    name?: string;
  }>({ open: false });

  const { data, isLoading } = useQuery({
    queryKey: ['ground-services'],
    queryFn: GroundServiceQuery.getAll,
  });

  const deleteMutation = useMutation({
    mutationFn: GroundServiceQuery.delete,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Ground service deleted successfully',
      });
      router.refresh();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete ground service',
        variant: 'destructive',
      });
    },
  });

  const handleAdd = () => {
    modal(<GroundServiceModal />, { ...DEFAULT_MODAL, width: '75%' }).open();
  };
  const handleEdit = (service: GroundService) => {
    modal(<GroundServiceModal serviceId={service.id} />, {
      ...DEFAULT_MODAL,
      width: '75%',
    }).open();
  };

  const columns = useMemo(
    () =>
      generateTableColumns<GroundService>(
        {
          name: { name: 'Name', type: 'text', sortable: true },
          description: { name: 'Description', type: 'text' },
          conditions: { name: 'Conditions', type: 'text' },
          flatRate: { name: 'Flat Rate', type: 'text' },
          localCartageAppliable: {
            name: 'Local Cartage',
            type: {
              type: 'node',
              render: ({ cell }) => (
                <div className="flex items-center whitespace-nowrap">
                  {cell ? (
                    <>
                      <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                      <span className="text-green-600 hidden">Yes</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="text-red-600 w-4 h-4 mr-1" />
                      <span className="text-red-600 hidden">No</span>
                    </>
                  )}
                </div>
              ),
            },
          },
        },
        {
          action: {
            name: 'Actions',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex gap-2">
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => handleEdit(row)}
                  >
                    <Edit2 className="size-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="destructive"
                    onClick={() =>
                      setDeleteDialog({
                        open: true,
                        id: row.id,
                        name: row.name,
                      })
                    }
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </div>
              ),
            },
          },
        },
        false,
      ),
    [deleteMutation],
  );

  const filters = useMemo(
    () =>
      generateTableFilters<GroundService>({
        name: { name: 'Name', type: 'text' },
        description: { name: 'Description', type: 'text' },
        localCartageAppliable: {
          name: 'Local Cartage',
          type: {
            type: 'select',
            options: [
              { label: 'Yes', value: 'true' },
              { label: 'No', value: 'false' },
            ],
          },
        },
      }),
    [],
  );

  return (
    <div>
      <DataTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <Button variant="main" onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            Add Ground Service
          </Button>
        }
      />
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog((d) => ({ ...d, open }))}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Ground Service</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete <b>{deleteDialog.name}</b>? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteDialog.id) deleteMutation.mutate(deleteDialog.id);
                setDeleteDialog({ open: false });
              }}
              disabled={deleteMutation.isPending}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

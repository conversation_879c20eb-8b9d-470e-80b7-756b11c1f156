'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { FaPlus } from 'react-icons/fa6';
import {
  Edit2,
  FileSpreadsheet,
  Mail,
  MoreHorizontal,
  Archive,
  ArchiveRestore,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { UserBriefData } from '@/models/User';
import RoleQuery from '@/services/queries/RoleQuery';
import UsersQuery from '@/services/queries/UsersQuery';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import UpdateRoleModal from '../update_role_modal';
import ExcelImportModal from '../excel-import/excel-import-modal';
import { cn } from '@/lib/utils';
import SwitchStatusModal from '../switch_status_modal';
import InviteUserModal from '../invite_user_modal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';

export default function UserTable() {
  const c = useTranslations('Common');

  const { data, isPending, isLoading } = useQuery({
    queryKey: UsersQuery.tags,
    queryFn: () => UsersQuery.getAll(),
  });

  const {
    data: roles,
    isLoading: isLoadingRoles,
    isSuccess: isSuccessRoles,
  } = useQuery({
    queryKey: RoleQuery.tags,
    queryFn: RoleQuery.getAll,
  });

  const newColumns = generateTableColumns<UserBriefData>(
    {
      id: { name: c('tables.id'), type: 'text', sortable: true },
      name: { name: c('tables.name'), type: 'text', sortable: true },
      username: { name: c('username'), type: 'text', sortable: true },
      email: { name: c('tables.email'), type: 'text', sortable: true },
      // phoneNumber: { name: c('tables.phoneNumber'), type: 'text' },
      role: {
        name: c('role'),
        type: {
          type: 'node',
          render: ({ cell, row }) => (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2 text-xs font-normal border border-dashed border-gray-300 hover:border-primary hover:bg-primary/5 flex items-center gap-1 group"
              onClick={() => {
                modal(
                  <UpdateRoleModal userId={row.id} currentRoleId={cell?.id} />,
                  DEFAULT_MODAL,
                ).open();
              }}
            >
              {cell?.name || c('unassigned')}
              <Edit2 className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity ml-1" />
            </Button>
          ),
        },
      },
      isVerified: {
        name: c('verified'),
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              <div
                className={cn(
                  'w-2 h-2 rounded-full mr-1 flex-shrink-0',
                  cell ? 'bg-green-500' : 'bg-red-500',
                )}
              />
              <span
                className={cn(
                  'text-xs font-medium px-2 py-0.5 rounded-full inline-block whitespace-nowrap',
                  cell
                    ? 'text-green-700 bg-green-50 border border-green-200'
                    : 'text-red-700 bg-red-50 border border-red-200',
                )}
              >
                {cell ? c('verified') : c('notVerified')}
              </span>
            </div>
          ),
        },
      },
    },
    {
      actions: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    modal(
                      ({ close }) => (
                        <InviteUserModal
                          close={close}
                          userId={row.id}
                          userName={row.name}
                          userEmail={row.email}
                        />
                      ),
                      { ...DEFAULT_MODAL, closable: true },
                    ).open();
                  }}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Invite
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/setup/user-management/${row.id}`}
                    className="flex items-center"
                  >
                    <Edit2 className="mr-2 h-4 w-4" />
                    {c('edit')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    modal(
                      ({ close }) => (
                        <SwitchStatusModal
                          close={close}
                          isChecked={!row.isArchived} // Invert the value for the modal
                          userId={row.id}
                        />
                      ),
                      { ...DEFAULT_MODAL, closable: false },
                    ).open();
                  }}
                >
                  {row.isArchived ? (
                    <>
                      <ArchiveRestore className="mr-2 h-4 w-4" />
                      Unarchive
                    </>
                  ) : (
                    <>
                      <Archive className="mr-2 h-4 w-4" />
                      Archive
                    </>
                  )}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<UserBriefData>({
    name: {
      name: c('tables.name'),
      type: 'text',
    },
    email: {
      name: c('personalEmail'),
      type: 'text',
    },
    isArchived: {
      name: 'Status',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'false' },
          { label: 'Archived', value: 'true' },
        ],
      },
    },
    role: {
      name: c('role'),
      type: {
        type: 'select',
        options:
          (isSuccessRoles &&
            roles.map((role) => ({
              label: role.name,
              value: role.name,
            }))) ||
          [],
      },
    },
  });
  return (
    <>
      <DataTable
        filterFields={filters}
        columns={newColumns}
        data={data}
        isLoading={isLoading || isPending || isLoadingRoles}
        controls={
          <div className="flex flex-row gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() =>
                modal(<ExcelImportModal />, {
                  ...DEFAULT_MODAL,
                  closable: true,
                }).open()
              }
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Import Users
            </Button>
            <Link href="/dashboard/setup/user-management/add">
              <Button variant="main">
                <FaPlus className="mr-2 h-4 w-4" />
                Add new user
              </Button>
            </Link>
          </div>
        }
      />
    </>
  );
}

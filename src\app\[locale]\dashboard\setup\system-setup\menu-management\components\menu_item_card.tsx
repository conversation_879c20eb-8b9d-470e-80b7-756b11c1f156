import { <PERSON>, EyeOff, ExternalLink, ArrowUp, ArrowDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/Badge';
import { useMutation } from '@tanstack/react-query';
import { getQueryClient } from '@/utils/query-client';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';
import { icons } from '@/common/icons';
import { MenuItem } from '@/models/MenuItem';
import MenuQuery from '@/services/queries/MenuQuery';

interface MenuItemCardProps {
  item: MenuItem;
  depth?: number;
  isFirst: boolean;
  isLast: boolean;
}

export default function MenuItemCard({
  item,
  depth = 0,
  isFirst,
  isLast,
}: MenuItemCardProps) {
  const { toast } = useToast();
  const { mutateAsync: moveUp, isPending: moveUpPending } = useMutation({
    mutationFn: () => MenuQuery.MoveUp(item.id),
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [MenuQuery.tags, 'brief'],
      });
      await getQueryClient().invalidateQueries({
        queryKey: MenuQuery.tags,
      });

      toast({
        variant: 'default',
        title: 'Moved Up Successfully',
      });
    },
  });
  const { mutateAsync: moveDown, isPending: moveDownPending } = useMutation({
    mutationFn: () => MenuQuery.MoveDown(item.id),
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [MenuQuery.tags, 'brief'],
      });
      await getQueryClient().invalidateQueries({
        queryKey: MenuQuery.tags,
      });

      toast({
        variant: 'default',
        title: 'Moved Down Successfully',
      });
    },
  });
  const Icon = item.icon ? (icons as any)[item.icon as any] : undefined;

  return (
    <div
      className={cn(
        'flex items-center gap-4 rounded-lg border bg-card p-4 transition-colors hover:bg-accent',
      )}
      style={{ marginLeft: `${depth * 2}rem` }}
    >
      <div className="flex flex-1 items-center gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-2">
            {Icon && <Icon className="h-6 w-6 text-muted-foreground" />}
            <span className="text-base font-medium">{item.name}</span>
          </div>
          <p className="text-sm text-muted-foreground">{item.description}</p>
        </div>
        <div className="flex items-center gap-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="secondary">{item.displayOrder}</Badge>
              </TooltipTrigger>
              <TooltipContent>Display Order</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {item.target === '_blank' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>Opens in new tab</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <div className="flex items-center gap-2">
            {item.isVisible ? (
              <Eye className="h-4 w-4 text-green-500" />
            ) : (
              <EyeOff className="h-4 w-4 text-red-500" />
            )}
            <span
              className={cn(
                'text-sm',
                item.isVisible ? 'text-green-500' : 'text-red-500',
              )}
            >
              {item.isVisible ? 'Visible' : 'Hidden'}
            </span>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="secondary"
          size="icon"
          className="h-8 w-8"
          onClick={() => moveUp()}
          disabled={moveUpPending || isFirst}
        >
          <ArrowUp className="h-4 w-4" />
        </Button>
        <Button
          variant="secondary"
          size="icon"
          className="h-8 w-8"
          onClick={() => moveDown()}
          disabled={moveDownPending || isLast}
        >
          <ArrowDown className="h-4 w-4" />
        </Button>
        <Link href={`/dashboard/setup/system-setup/menu-management/${item.id}`}>
          <Button variant="outline" size="sm">
            Open
          </Button>
        </Link>
      </div>
    </div>
  );
}

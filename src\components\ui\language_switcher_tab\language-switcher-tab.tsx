import { useState } from 'react';
import { Control } from 'react-hook-form';

import Field from '@/components/ui/inputs/field';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

import { InputType } from '../inputs/field/field';

interface ILanguageSwitcherTab {
  languages: string[];
  fields: {
    type: InputType;
    name: string;
    label: string;
    required?: boolean;
    control: Control<any, object>;
    isCommonField?: boolean;
  }[];
}

function LanguageSwitcherTab({ languages, fields }: ILanguageSwitcherTab) {
  const [selectedTab, setSelectedTab] = useState(languages[0]);

  return (
    <div className="">
      <Tabs value={selectedTab} className="w-full flex flex-col gap-6">
        <TabsList className="grid w-fit grid-cols-2 ml-auto">
          {languages.map((language) => (
            <TabsTrigger
              key={language}
              value={language}
              onClick={() => setSelectedTab(language)}
              className="data-[state=active]:bg-primary"
            >
              {language}
            </TabsTrigger>
          ))}
        </TabsList>
        {languages.map((language) => (
          <TabsContent key={language} value={language}>
            <div className="grid gap-6 md:grid-cols-2 ">
              {fields.map((field) => (
                <Field
                  key={`${field.name}_${language}`}
                  control={field.control}
                  name={
                    field.isCommonField
                      ? field.name
                      : `${field.name}_${language}`
                  }
                  label={`${field.label}`}
                  required={field.required}
                  type={field.type}
                />
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

export default LanguageSwitcherTab;

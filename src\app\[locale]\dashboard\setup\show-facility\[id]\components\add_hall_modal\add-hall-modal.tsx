'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import { ShowHallSchema, ShowHallData } from '@/schema/ShowHallSchema';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import { getQueryClient } from '@/utils/query-client';
import { DropzoneOptions } from 'react-dropzone';

function FormContent({
  defaultValues,
  locationId,
  id,
}: {
  defaultValues?: ShowHallData;
  locationId: number;
  id?: number;
}) {
  const { toast } = useToast();
  const form = useForm<ShowHallData>({
    resolver: zodResolver(ShowHallSchema),
    defaultValues: {
      locationId: Number(locationId),
      hallName: defaultValues?.hallName.toString() ?? '',
      hallCode: defaultValues?.hallCode?.toString() ?? '',
      hallStyle: defaultValues?.hallStyle?.toString() ?? '',
      hallFloorType: defaultValues?.hallFloorType?.toString() ?? '',
      banquetCapacity: defaultValues?.banquetCapacity?.toString() ?? '0',
      hallWidth: defaultValues?.hallWidth?.toString() ?? '0',
      hallLength: defaultValues?.hallLength?.toString() ?? '0',
      overheadHeight: defaultValues?.overheadHeight?.toString() ?? '0',
      hallArea: defaultValues?.hallArea?.toString() ?? '0',
      isElecOnFloor: defaultValues?.isElecOnFloor ?? false,
      isElecOnCeiling: defaultValues?.isElecOnCeiling ?? false,
      hallSurface: defaultValues?.hallSurface?.toString() ?? '0',
      hallCeilingHeight: defaultValues?.hallCeilingHeight?.toString() ?? '0',
      accessDoor: defaultValues?.accessDoor?.toString() ?? '',
      loadingDocks: defaultValues?.loadingDocks?.toString() ?? '0',
      hallBoothCount: defaultValues?.hallBoothCount?.toString() ?? '0',
      isArchived: defaultValues?.isArchived ?? false,
      floorPlan: defaultValues?.floorPlan ?? [],
    },
  });
  const dropzone = {
    accept: {
      'application/pdf': ['.pdf'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const { mutate } = useMutation({
    mutationFn: id ? ShowHallQuery.editHall(id) : ShowHallQuery.createHall,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: ['ShowHall', { location: Number(locationId) }],
      });

      await getQueryClient().invalidateQueries({
        queryKey: ['ShowHall', { hallId: Number(id) }],
      });

      toast({
        title: 'Success',
        description: id
          ? 'Hall updated successfully'
          : 'New hall added successfully',
        variant: 'success',
      });

      modal.close();
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        className="w-full max-w-3xl"
        title={id ? 'Update Hall' : 'Add Hall'}
        description={id ? 'Update hall details' : 'Create new hall entry'}
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end w-full gap-4">
            <Button variant="main">{id ? 'Update' : 'Add'}</Button>
          </div>
        }
      >
        {/* Column 1 */}
        <div className="flex flex-col gap-4">
          {/* Basic Info Section */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium border-b pb-1">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <Field
                control={form.control}
                name="hallName"
                label="Hall Name"
                placeholder="Enter Hall Name"
                type="text"
                required
              />
              <Field
                control={form.control}
                name="hallCode"
                label="Hall Code"
                placeholder="Enter Hall Code"
                disabled={id ? true : false}
                type="text"
                required
              />
              <Field
                control={form.control}
                name="hallStyle"
                label="Style"
                placeholder="Enter Hall Style"
                type="text"
              />
              <Field
                control={form.control}
                name="hallFloorType"
                label="Floor Type"
                placeholder="Enter Hall Floor Type"
                type="text"
              />
            </div>
            <Field
              control={form.control}
              name="floorPlan"
              label={id ? 'Replace Floor Plan' : 'Floor Plan'}
              required={id ? true : false}
              type={{
                type: 'file',
                props: {
                  dropzoneOptions: dropzone,
                },
              }}
            />
            {id && (
              <Field
                control={form.control}
                name="isArchived"
                label="Archived"
                type="checkbox"
              />
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-3">
              <h3 className="text-lg font-medium border-b pb-1">
                Dimensions & Capacity
              </h3>
              <Field
                control={form.control}
                name="banquetCapacity"
                label="Banquet Capacity"
                type="number"
              />
              <Field
                control={form.control}
                name="hallWidth"
                label="Width (m)"
                type="number"
              />
              <Field
                control={form.control}
                name="hallLength"
                label="Length (m)"
                type="number"
              />
              <Field
                control={form.control}
                name="overheadHeight"
                label="Overhead Height (m)"
                type="number"
              />
            </div>
            {/* Space & Structure */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium border-b pb-1">
                Structure & Area
              </h3>
              <Field
                control={form.control}
                name="hallArea"
                label="Area (sq m)"
                type="number"
              />
              <Field
                control={form.control}
                name="hallSurface"
                label="Surface"
                type="text"
              />
              <Field
                control={form.control}
                name="hallCeilingHeight"
                label="Ceiling Height"
                type="number"
              />
            </div>

            {/* Access & Utilities */}
            <div className="space-y-3">
              <h3 className="text-lg font-medium border-b pb-1">
                Access & Utilities
              </h3>
              <Field
                control={form.control}
                name="accessDoor"
                label="Access Door"
                type="text"
              />
              <Field
                control={form.control}
                name="loadingDocks"
                label="Loading Docks"
                type="number"
              />
              <Field
                control={form.control}
                name="hallBoothCount"
                label="Booth Count"
                type="number"
              />
              <Field
                control={form.control}
                name="isElecOnFloor"
                label="Electricity on Floor"
                type="checkbox"
              />
              <Field
                control={form.control}
                name="isElecOnCeiling"
                label="Electricity on Ceiling"
                type="checkbox"
              />
            </div>
          </div>
        </div>
      </ModalContainer>
    </Form>
  );
}

interface AddHallModalProps {
  locationId: number;
  hallId?: number;
}

function AddHallModal({ locationId, hallId }: AddHallModalProps) {
  const {
    data: hall,
    isLoading,
    isPaused,
  } = useQuery({
    queryKey: ['ShowHall', { hallId: Number(hallId) }],
    queryFn: () => ShowHallQuery.get(Number(hallId!)),
    enabled: !!hallId,
    select: (data: ShowHallData) => {
      return {
        locationId: locationId,
        hallName: data?.hallName ?? '',
        hallCode: data?.hallCode ?? '',
        hallStyle: data?.hallStyle ?? '',
        hallFloorType: data?.hallFloorType ?? '',
        banquetCapacity: data?.banquetCapacity ?? '',
        hallWidth: data?.hallWidth ?? '',
        hallLength: data?.hallLength ?? '',
        overheadHeight: data?.overheadHeight ?? '',
        hallArea: data?.hallArea ?? '',
        hallSurface: data?.hallSurface ?? '',
        hallCeilingHeight: data?.hallCeilingHeight ?? '',
        accessDoor: data?.accessDoor ?? '',
        loadingDocks: data?.loadingDocks ?? '',
        hallBoothCount: data?.hallBoothCount ?? '',
        isElecOnFloor: data?.isElecOnFloor ?? false,
        isElecOnCeiling: data?.isElecOnCeiling ?? false,
        isArchived: data?.isArchived ?? false,
        floorPlan: data.floorPlan,
        floorPlanPath: data.floorPlanPath ?? '',
      } as ShowHallData;
    },
  });
  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent id={hallId} locationId={locationId} defaultValues={hall} />
      )}
    </Suspense>
  );
}

export default AddHallModal;

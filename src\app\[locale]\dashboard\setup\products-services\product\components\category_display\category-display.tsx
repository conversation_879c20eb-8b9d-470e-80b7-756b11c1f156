'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { FaPlus } from 'react-icons/fa';
import { CategoryWithOfferingsDto } from '@/models/Offering';
import OfferingDisplay from '../offering_display';
interface GroupDisplayProps {
  data: CategoryWithOfferingsDto;
  groupId: number;
  categoryId: number;
}

const GroupDisplay: React.FC<GroupDisplayProps> = ({
  data,
  groupId,
  categoryId,
}) => {
  return (
    <Accordion
      key={data.categoryId}
      type="single"
      collapsible
      className="space-y-3"
    >
      <AccordionItem value={data.categoryName + data.categoryId.toString()}>
        <AccordionTrigger
          className={`px-3 py-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg w-full`}
        >
          <div className="flex flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-1 flex-1 min-w-0">
              <Link
                href={`/dashboard/setup/products-services/category/${categoryId}`}
              >
                <span
                  className={`text-md hover:text-main hover:underline font-medium ${data.offerings.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 ${!data.isAvailable && 'line-through'}`}
                >
                  {data.categoryName}
                </span>
              </Link>
              {/* {data.offerings.length === 0 && (
                <span className="text-xs text-gray-600 line-through">
                  No products available
                </span>
              )} */}
            </div>
            {data.isAvailable && (
              <div className="flex items-center gap-1">
                <Link
                  href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/add`}
                >
                  <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                    <FaPlus className="h-3 w-3" />
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-3">
          {data && data.offerings.length > 0 ? (
            data.offerings.map((offering) => (
              <OfferingDisplay
                key={offering.id}
                id={offering.id}
                name={offering.name}
                groupId={groupId}
                categoryId={data.categoryId}
                options={offering.options}
              />
            ))
          ) : (
            <div className="text-sm text-gray-500 italic">
              No products in this category.
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default GroupDisplay;

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { OfferingCreateSchema } from '@/schema/OfferingSchema';
import OfferingQuery from '@/services/queries/OfferingQuery';
import { getQueryClient } from '@/utils/query-client';

interface RichTextDescriptionProps {
  id?: number;
  groupId: number;
  categoryId: number;
}

type DescriptionFields = {
  publicDescription: string;
  internalDescription: string;
};

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues: DescriptionFields;
  id: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();

  const form = useForm<DescriptionFields>({
    resolver: zodResolver(
      OfferingCreateSchema.pick({
        publicDescription: true,
        internalDescription: true,
      }),
    ),
    defaultValues,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (data: DescriptionFields) =>
      OfferingQuery.updateDescription(id, data),
    onSuccess: async () => {
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Offering', { id }],
        });
      }
      toast({
        title: 'Success',
        description: 'Description updated successfully.',
        variant: 'success',
      });
      push(`/dashboard/setup/products-services/product`);
    },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <Field
          control={form.control}
          name="publicDescription"
          label="Public Description"
          type="LightRichText"
        />
        <Field
          control={form.control}
          name="internalDescription"
          label="Internal Description"
          type="LightRichText"
        />
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => push('/dashboard/setup/products-services/product')}
          >
            Cancel
          </Button>
          <Button variant="main" type="submit" disabled={isPending}>
            {isPending && <Spinner className="mr-2" />}
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function RichTextDescription({ id }: RichTextDescriptionProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Offering', { id }],
    queryFn: () => OfferingQuery.getById(id!),
    enabled: !!id,
    select: (res): DescriptionFields => ({
      publicDescription: res.publicDescription ?? '',
      internalDescription: res.internalDescription ?? '',
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {id && data && <FormContent defaultValues={data} id={id} />}
    </Suspense>
  );
}

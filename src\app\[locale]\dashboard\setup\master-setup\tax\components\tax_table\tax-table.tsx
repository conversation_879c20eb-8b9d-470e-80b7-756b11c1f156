'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { Tax } from '@/models/Tax';
import TaxQuery from '@/services/queries/TaxQuery';
import { Button } from '@/components/ui/button';
import TaxModal from '../tax_modal';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';

export const TaxTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: TaxQuery.tags,
    queryFn: TaxQuery.getAll,
  });

  const {
    data: provinces,
    isLoading: isLoadingProvinces,
    isSuccess: isSuccessProvinces,
  } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const {
    data: taxTypes,
    isLoading: isLoadingTaxTypes,
    isSuccess: isSuccessTaxTypes,
  } = useQuery({
    queryKey: ['Tax Types'],
    queryFn: TaxQuery.getTaxType,
  });

  const columns = generateTableColumns<Tax>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      taxProvince: { name: 'Province', type: 'text', sortable: true },
      taxType: { name: 'Tax Type', type: 'text', sortable: true },
      taxTypeAbr: { name: 'Abbreviation', type: 'text', sortable: true },
      taxRate: { name: 'Rate', type: 'text', sortable: true },
      displayOrder: { name: 'Display Order', type: 'text', sortable: true },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full justify-center">
              <Button
                variant="secondary"
                size="icon"
                onClick={() => {
                  modal(<TaxModal id={row.id} />, DEFAULT_MODAL).open();
                }}
              >
                <Edit2 className="size-4" />
              </Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Tax>({
    taxProvince: {
      name: 'Province',
      type: {
        type: 'select',
        options:
          (provinces &&
            provinces.map((o) => ({
              label: o.name,
              value: o.name,
            }))) ||
          [],
      },
    },
    taxType: {
      name: 'Tax Type',
      type: {
        type: 'select',
        options:
          (taxTypes &&
            taxTypes.map((o) => ({
              label: o.name,
              value: o.name,
            }))) ||
          [],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Button
            variant="main"
            onClick={() => {
              modal(<TaxModal />, {
                ...DEFAULT_MODAL,
                width: '40%',
              }).open();
            }}
          >
            <FaPlus />
            Add New Tax
          </Button>
        </div>
      }
    />
  );
};

export default TaxTable;

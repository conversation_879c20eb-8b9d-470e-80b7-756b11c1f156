import { useTranslations } from 'next-intl';
import { Accept } from 'react-dropzone';

interface IFileSvgDraw {
  accept?: Accept;
}

function FileSvgDraw({
  accept = { 'image/*': ['.jpg', '.jpeg', '.png'] },
}: IFileSvgDraw) {
  const c = useTranslations('Common');
  return (
    <>
      <svg
        className="w-8 h-8 mb-3 text-gray-500 dark:text-gray-400"
        aria-hidden="true"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 20 16"
      >
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
        />
      </svg>
      <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
        <span className="font-semibold">{c('clickToUpload')}</span>
        &nbsp; {c('dragAndDrop')}
      </p>
      <p className="text-xs text-gray-500 dark:text-gray-400 uppercase">
        {Object.keys(accept)
          .map((key) => accept[key])
          .join(', ')}
      </p>
    </>
  );
}

export default FileSvgDraw;

'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2, Trash2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { Button } from '@/components/ui/button';
import { DocumentFileTypeInList } from '@/models/DocumentFileType';
import { Link } from '@/utils/navigation';
import { Badge } from '@/components/ui/Badge';
import { getQueryClient } from '@/utils/query-client';

export default function DocumentFileTypeTable() {
  const { data: documentFileTypes, isLoading } = useQuery({
    queryKey: DocumentFileTypeQuery.tags,
    queryFn: () => DocumentFileTypeQuery.getAll(),
  });

  const columns = generateTableColumns<DocumentFileTypeInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      extensionCode: { name: 'Extension Code', type: 'text', sortable: true },
      extension: { name: 'Extension', type: 'text', sortable: true },
      isImage: {
        name: 'Is Image',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Badge variant={row.isImage ? 'default' : 'secondary'}>
              {row.isImage ? 'Yes' : 'No'}
            </Badge>
          ),
        },
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Link
                href={`/dashboard/setup/master-setup/document-file-types/${row.id ?? 'add'}`}
              >
                <Button variant="secondary" size="icon">
                  <Edit2 className="size-4" />
                </Button>
              </Link>
              <Button
                variant="destructive"
                size="icon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Document File Type"
                        description={`Are you sure you want to delete "${row.name}"? This action cannot be undone.`}
                        mutateFn={() => DocumentFileTypeQuery.delete(row.id!)}
                        mutationKey={[...DocumentFileTypeQuery.tags]}
                        onSuccess={async () => {
                          await getQueryClient().invalidateQueries({
                            queryKey: [...DocumentFileTypeQuery.tags],
                          });
                        }}
                        danger
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              >
                <Trash2 className="size-4" />
              </Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<DocumentFileTypeInList>({
    name: { name: 'Name', type: 'text' },
    extensionCode: { name: 'Extension Code', type: 'text' },
    extension: { name: 'Extension', type: 'text' },
    isImage: {
      name: 'Is Image',
      type: {
        type: 'select',
        options: [
          { label: 'All', value: '' },
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Document File Types</h1>
      </div>

      <DataTable
        columns={columns}
        data={documentFileTypes || []}
        filterFields={filters}
        isLoading={isLoading}
        controls={
          <Link href="/dashboard/setup/master-setup/document-file-types/add">
            <Button>
              <FaPlus className="mr-2 h-4 w-4" />
              Add File Type
            </Button>
          </Link>
        }
      />
    </div>
  );
}

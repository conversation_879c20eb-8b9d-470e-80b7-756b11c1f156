'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { getQueryClient } from '@/utils/query-client';
import Field from '@/components/ui/inputs/field';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { WarehouseData, WarehouseSchema } from '@/schema/WarehouseSchema';
import { WarehouseDetailDto } from '@/models/Warehouse';
import UsersQuery from '@/services/queries/UsersQuery';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: WarehouseData;
  id?: number;
}) {
  const { toast } = useToast();

  const form = useForm<WarehouseData>({
    resolver: zodResolver(WarehouseSchema),
    defaultValues: {
      code: defaultValues?.code ?? '',
      warehouseName: defaultValues?.warehouseName ?? '',
      addressLine1: defaultValues?.addressLine1 ?? '',
      addressLine2: defaultValues?.addressLine2 ?? '',
      city: defaultValues?.city ?? '',
      postalCode: defaultValues?.postalCode ?? '',
      provinceId: defaultValues?.provinceId?.toString() ?? undefined,
      countryId: defaultValues?.countryId?.toString() ?? undefined,
      warehouseTypeId: defaultValues?.warehouseTypeId?.toString() ?? '2',
      contactPersonId: defaultValues?.contactPersonId?.toString() ?? undefined,
      phone: defaultValues?.phone ?? '',
      isActive: defaultValues?.isActive ?? true,
    },
  });

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const { data: users, isLoading: isLoadingUsers } = useQuery({
    queryKey: [...UsersQuery.tags, { groupIds: [4, 5] }],
    queryFn: () => UsersQuery.getUsersByGroup([4, 5]),
  });

  const { data: types, isLoading: isLoadingTypes } = useQuery({
    queryKey: WarehouseQuery.tags,
    queryFn: WarehouseQuery.getAllType,
  });

  const { mutate } = useMutation({
    mutationFn: id ? WarehouseQuery.update(id) : WarehouseQuery.add,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
      });
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Warehouse Info', { id }],
        });
      }
      toast({
        title: 'Success',
        description: id
          ? 'Warehouse updated successfully'
          : 'New warehouse created',
        variant: 'success',
      });
      modal.close();
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        className="max-w-[600px]"
        title={id ? 'Update Warehouse' : 'Add Warehouse'}
        description={
          id ? 'Update warehouse details' : 'Create new warehouse entry'
        }
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end w-full gap-4">
            <Button variant="main">{id ? 'Update' : 'Add'}</Button>
          </div>
        }
      >
        <div className="flex flex-col gap-2 mt-4">
          {/* <Field
            control={form.control}
            name="warehouseTypeId"
            label="Warehouse Type"
            required
            type={{
              type: 'select',
              props: {
                options:
                  types?.map((type) => ({
                    label: type.name,
                    value: type.id.toString(),
                  })) ?? [],
                placeholder: 'Select a Warehouse Type',
              },
            }}
          /> */}
          {id && (
            <Field
              control={form.control}
              name="code"
              label="Code"
              type="text"
              required
              disabled
            />
          )}
          <Field
            control={form.control}
            name="warehouseName"
            label="Warehouse Name"
            type="text"
            required
          />
          <Field
            control={form.control}
            name="addressLine1"
            label="Address Line 1"
            type="text"
          />
          <Field
            control={form.control}
            name="addressLine2"
            label="Address Line 2"
            type="text"
          />
          <Field control={form.control} name="city" label="City" type="text" />
          <Field
            control={form.control}
            name="postalCode"
            label="Postal Code"
            type="text"
          />
          <Field
            control={form.control}
            name="provinceId"
            label="Province"
            required
            type={{
              type: 'select',
              props: {
                options:
                  provinces?.map((p) => ({
                    label: p.name,
                    value: p.id.toString(),
                  })) ?? [],
                placeholder: 'Select Province',
              },
            }}
          />
          <Field
            control={form.control}
            name="countryId"
            label="Country"
            required
            type={{
              type: 'select',
              props: {
                options:
                  countries?.map((c) => ({
                    label: c.name,
                    value: c.id.toString(),
                  })) ?? [],
                placeholder: 'Select Country',
              },
            }}
          />
          <Field
            control={form.control}
            name="phone"
            label="Phone"
            type="text"
          />
          <Field
            control={form.control}
            name="contactPersonId"
            label="Contact Person"
            type={{
              type: 'select',
              props: {
                options:
                  users?.map((person) => ({
                    label: person.name,
                    value: person.id.toString(),
                  })) ?? [],
                placeholder: 'Select Contact Person',
              },
            }}
          />

          <Field
            control={form.control}
            name="isActive"
            label="Active"
            type="checkbox"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

interface InventoryModalProps {
  id?: number;
}

function InventoryModal({ id }: InventoryModalProps) {
  const {
    data: warehouse,
    isPaused,
    isLoading,
  } = useQuery({
    queryKey: ['Warehouse Info', { id }],
    queryFn: () => WarehouseQuery.get(Number(id!)),
    enabled: !!id,
    select: (data: WarehouseDetailDto) => {
      return {
        code: data.code,
        warehouseName: data.warehouseName ?? '',
        addressLine1: data.addressline1 ?? null,
        addressLine2: data.addressline2 ?? null,
        city: data.city ?? null,
        postalCode: data.postalCode ?? null,
        provinceId: data.provinceId?.toString() ?? undefined,
        countryId: data.countryId?.toString() ?? undefined,
        warehouseTypeId: data.warehousetypeId?.toString() ?? undefined,
        contactPersonId: data.contactpersonId?.toString() ?? undefined,
        phone: data.phone.toString() ?? null,
        isActive: data.isActive ?? false,
      };
    },
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent
          defaultValues={warehouse}
          id={warehouse ? Number(id) : undefined}
        />
      )}
    </Suspense>
  );
}

export default InventoryModal;

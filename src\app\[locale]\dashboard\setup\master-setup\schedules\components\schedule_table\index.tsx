'use client';

import { useQuery, useMutation } from '@tanstack/react-query';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Plus, Edit2, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { Schedule } from '@/models/Schedule';
import ScheduleQuery from '@/services/queries/ScheduleQuery';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import SwitchStatusModal from './switch_status_modal';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';

export default function ScheduleTable() {
  const c = useTranslations('Common');
  const router = useRouter();
  const { toast } = useToast();

  const { data, isPending, isLoading } = useQuery({
    queryKey: ScheduleQuery.tags,
    queryFn: () => ScheduleQuery.getAll(),
  });

  const deleteMutation = useMutation({
    mutationFn: ScheduleQuery.delete,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Schedule deleted successfully',
      });
      router.refresh();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete schedule',
        variant: 'destructive',
      });
    },
  });

  const columns = generateTableColumns<Schedule>(
    {
      id: { name: c('tables.id'), type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: c('tables.name'), type: 'text', sortable: true },
      description: {
        name: c('tables.description'),
        type: 'text',
        sortable: true,
      },
      isActive: {
        name: c('tables.status'),
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              <div
                className={cn(
                  'w-2 h-2 rounded-full mr-1 flex-shrink-0',
                  cell ? 'bg-green-500' : 'bg-red-500',
                )}
              />
              <span
                className={cn(
                  'text-xs font-medium px-2 py-0.5 rounded-full inline-block whitespace-nowrap',
                  cell
                    ? 'text-green-700 bg-green-50 border border-green-200'
                    : 'text-red-700 bg-red-50 border border-red-200',
                )}
              >
                {cell ? c('active') : c('inactive')}
              </span>
            </div>
          ),
        },
      },
      createdAt: {
        name: c('tables.createdAt'),
        type: 'text',
        sortable: true,
      },
    },
    {
      'toggle-action': {
        name: c('tables.status'),
        type: {
          type: 'node',
          render: ({ row }) => (
            <Switch
              checked={row.isActive}
              onCheckedChange={(isActive) => {
                modal(
                  ({ close }) => (
                    <SwitchStatusModal
                      close={close}
                      isChecked={isActive}
                      scheduleId={row.id}
                    />
                  ),
                  { ...DEFAULT_MODAL, closable: false },
                ).open();
              }}
            />
          ),
        },
      },
      'edit-action': {
        name: c('edit'),
        type: {
          type: 'node',
          render: ({ row }) => (
            <Link href={`/dashboard/setup/master-setup/schedules/${row.id}`}>
              <Button variant="secondary" size="icon">
                <Edit2 className="size-4" />
              </Button>
            </Link>
          ),
        },
      },
      'delete-action': {
        name: c('delete'),
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              variant="outline"
              size="icon"
              className="text-red-600 hover:text-red-700"
              onClick={() => {
                if (confirm(`Are you sure you want to delete ${row.name}?`)) {
                  deleteMutation.mutate(row.id);
                }
              }}
              disabled={deleteMutation.isPending}
            >
              <Trash2 className="size-4" />
            </Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Schedule>({
    name: {
      name: c('tables.name'),
      type: 'text',
    },
    code: {
      name: 'Code',
      type: 'text',
    },
    description: {
      name: c('tables.description'),
      type: 'text',
    },
    isActive: {
      name: c('tables.status'),
      type: {
        type: 'select',
        options: [
          { label: c('active'), value: 'true' },
          { label: c('inactive'), value: 'false' },
        ],
      },
    },
  });

  return (
    <>
      <DataTable
        filterFields={filters}
        columns={columns}
        data={data}
        isLoading={isLoading || isPending}
        controls={
          <div className="flex flex-row gap-2 justify-end">
            <Link href="/dashboard/setup/master-setup/schedules/add">
              <Button variant="main">
                <Plus className="mr-2 h-4 w-4" />
                Add Schedule
              </Button>
            </Link>
          </div>
        }
      />
    </>
  );
}

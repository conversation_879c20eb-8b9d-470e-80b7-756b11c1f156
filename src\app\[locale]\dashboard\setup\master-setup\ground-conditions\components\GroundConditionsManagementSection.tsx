'use client';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import GroundConditionQuery from '@/services/queries/GroundConditionQuery';
import { GroundCondition } from '@/models/GroundCondition';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import GroundConditionModal from './GroundConditionModal';
import { useMemo, useState } from 'react';
import { Edit2, Trash2, Plus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import ModalContainer from '@/components/ui/overlay/components/modal_container';

function DescriptionCell({ html }: { html: string }) {
  const handleViewMore = (e: React.MouseEvent) => {
    e.stopPropagation();
    modal(
      <ModalContainer title="Description">
        <div
          style={{ whiteSpace: 'pre-line' }}
          dangerouslySetInnerHTML={{ __html: html || '' }}
        />
      </ModalContainer>,
      { ...DEFAULT_MODAL, width: '40%' },
    ).open();
  };
  return (
    <div style={{ maxWidth: 400 }}>
      <div
        style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'normal',
        }}
        dangerouslySetInnerHTML={{ __html: html || '' }}
      />
      <button
        type="button"
        style={{
          marginTop: 4,
          background: 'none',
          border: 'none',
          color: '#0070f3',
          cursor: 'pointer',
          fontSize: '13px',
          padding: 0,
          textDecoration: 'underline',
        }}
        onClick={handleViewMore}
      >
        View More
      </button>
    </div>
  );
}

export default function GroundConditionsManagementSection() {
  const router = useRouter();
  const { toast } = useToast();

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    id?: number;
    name?: string;
  }>({ open: false });

  const { data, isLoading } = useQuery({
    queryKey: ['ground-conditions'],
    queryFn: GroundConditionQuery.getAll,
  });

  const deleteMutation = useMutation({
    mutationFn: GroundConditionQuery.delete,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Ground condition deleted successfully',
      });
      router.refresh();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete ground condition',
        variant: 'destructive',
      });
    },
  });

  const handleAdd = () => {
    modal(<GroundConditionModal />, { ...DEFAULT_MODAL, width: '75%' }).open();
  };
  const handleEdit = (condition: GroundCondition) => {
    modal(<GroundConditionModal conditionId={condition.id} />, {
      ...DEFAULT_MODAL,
      width: '75%',
    }).open();
  };

  const columns = useMemo(
    () =>
      generateTableColumns<GroundCondition>(
        {
          name: { name: 'Name', type: 'text', sortable: true },
          text: {
            name: 'Description',
            type: {
              type: 'node',
              render: ({ cell }: { cell: string }) => (
                <DescriptionCell html={cell} />
              ),
            },
          },
        },
        {
          action: {
            name: 'Actions',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex gap-2">
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => handleEdit(row)}
                  >
                    <Edit2 className="size-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="destructive"
                    onClick={() =>
                      setDeleteDialog({
                        open: true,
                        id: row.id,
                        name: row.name,
                      })
                    }
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </div>
              ),
            },
          },
        },
        false,
      ),
    [deleteMutation],
  );

  const filters = useMemo(
    () =>
      generateTableFilters<GroundCondition>({
        name: { name: 'Name', type: 'text' },
        text: { name: 'Description', type: 'text' },
      }),
    [],
  );

  return (
    <div>
      <DataTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <Button variant="main" onClick={handleAdd}>
            <Plus className="mr-2 h-4 w-4" />
            Add Ground Condition
          </Button>
        }
      />
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog((d) => ({ ...d, open }))}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Ground Condition</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete <b>{deleteDialog.name}</b>? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteDialog.id) deleteMutation.mutate(deleteDialog.id);
                setDeleteDialog({ open: false });
              }}
              disabled={deleteMutation.isPending}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

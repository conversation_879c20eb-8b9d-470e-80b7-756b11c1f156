'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, Edit2, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import ShowContactQuery from '@/services/queries/ShowContactQuery';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { Contact } from '@/models/Contact';
import AddShowContactModal from '../add_show_contact_modal';

interface ShowContactTableProps {
  locationId: number;
}

export const ShowContactTable = ({ locationId }: ShowContactTableProps) => {
  const { data, isLoading } = useQuery({
    queryKey: ['ShowContact', { locationId }],
    queryFn: () => ShowContactQuery.getByLocation(locationId),
    enabled: !!locationId,
  });

  const columns = generateTableColumns<Contact>(
    {
      id: { name: 'ID', type: 'text' },
      firstName: { name: 'First Name', type: 'text' },
      lastName: { name: 'Last Name', type: 'text' },
      email: { name: 'Email', type: 'text' },
      telephone: { name: 'Telephone', type: 'text' },
      cellphone: { name: 'Cellphone', type: 'text' },
      isArchived: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <XCircle className="text-red-600 w-4 h-4" />
              ) : (
                <CheckCircle className="text-green-600 w-4 h-4" />
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex gap-2 justify-center">
              <Button
                variant="secondary"
                size="icon"
                onClick={() => {
                  modal(
                    <AddShowContactModal
                      contactId={row.id}
                      locationId={locationId}
                    />,
                    {
                      ...DEFAULT_MODAL,
                      width: 'w-full',
                    },
                  ).open();
                }}
              >
                <Edit2 className="size-4" />
              </Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Contact>({
    firstName: {
      name: 'First Name',
      type: 'text',
    },
    lastName: {
      name: 'Last Name',
      type: 'text',
    },
    email: {
      name: 'Email',
      type: 'text',
    },
    isArchived: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'false' },
          { label: 'Inactive', value: 'true' },
        ],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      filterFields={filters}
      data={data}
      isLoading={isLoading}
      controls={
        <div className="flex justify-end gap-2">
          <Button
            variant="main"
            onClick={() => {
              modal(<AddShowContactModal locationId={locationId} />, {
                ...DEFAULT_MODAL,
                width: 'w-full',
              }).open();
            }}
          >
            <FaPlus />
            Add New Contact
          </Button>
        </div>
      }
    />
  );
};

export default ShowContactTable;

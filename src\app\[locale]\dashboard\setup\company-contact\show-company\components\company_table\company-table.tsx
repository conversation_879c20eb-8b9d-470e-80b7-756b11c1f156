'use client';

import { useQuery } from '@tanstack/react-query';
import { useState, useMemo } from 'react';
import { Edit2, Trash2, Building2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import Link from 'next/link';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import ContactsTable from '../contacts_table/contacts-table';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { getQueryClient } from '@/utils/query-client';
import CompanyFiltersComponent, { CompanyFilters } from './company-filters';

export const CompanyTable = () => {
  const [filters, setFilters] = useState<CompanyFilters>({
    name: '',
    city: '',
    province: '',
    showArchived: true,
  });

  const { data, isLoading } = useQuery({
    queryKey: [...CompanyQuery.tags, 'Show manager'],
    queryFn: () => CompanyQuery.getAll('Show manager'),
  });

  const filteredCompanies = useMemo(() => {
    if (!data) return [];

    return data.filter((company) => {
      if (
        filters.name &&
        filters.name.trim() !== '' &&
        !company.name.toLowerCase().includes(filters.name.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.city &&
        filters.city.trim() !== '' &&
        company.city &&
        !company.city.toLowerCase().includes(filters.city.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.province &&
        filters.province.trim() !== '' &&
        company.province &&
        !company.province.toLowerCase().includes(filters.province.toLowerCase())
      ) {
        return false;
      }

      if (filters.showArchived === false && company.isArchived) {
        return false;
      }

      return true;
    });
  }, [data, filters]);

  const handleFilterChange = (newFilters: CompanyFilters) => {
    setFilters(newFilters);
  };

  const resetFilters = () => {
    setFilters({
      name: '',
      city: '',
      province: '',
      showArchived: true,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-end mb-6">
          <Link href="/dashboard/setup/company-contact/show-company/add">
            <Button variant="main" size="sm">
              <FaPlus className="mr-1 h-3 w-3" />
              Add New Company
            </Button>
          </Link>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="bg-white border border-gray-200 rounded-lg p-4 animate-pulse"
            >
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex justify-end mb-6">
          <Link href="/dashboard/setup/company-contact/show-company/add">
            <Button variant="main" size="sm">
              <FaPlus className="mr-1 h-3 w-3" />
              Add New Company
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No companies found
          </h3>
          <p className="text-gray-500">
            Get started by adding your first company.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-start mb-6 gap-5  ">
        <CompanyFiltersComponent
          filters={filters}
          onFilterChange={handleFilterChange}
          onResetFilters={resetFilters}
          companies={data || []}
        />
        <Link href="/dashboard/setup/company-contact/show-company/add">
          <Button variant="main" size="sm">
            <FaPlus className="mr-1 h-3 w-3" />
            Add New Company
          </Button>
        </Link>
      </div>

      {filteredCompanies.length === 0 ? (
        <div className="text-center py-8 bg-white border border-gray-200 rounded-lg">
          <Building2 className="h-10 w-10 text-gray-400 mx-auto mb-3" />
          <h3 className="text-md font-medium text-gray-900 mb-1">
            No companies match your filters
          </h3>
          <p className="text-sm text-gray-500">
            Try adjusting your filter criteria or clear filters.
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
            className="mt-3"
          >
            Clear Filters
          </Button>
        </div>
      ) : (
        <Accordion type="multiple" className="space-y-3">
          {filteredCompanies.map((company) => (
            <AccordionItem
              key={company.id}
              value={company.id.toString()}
              className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <AccordionTrigger className="px-3 py-1 hover:no-underline [&[data-state=open]>div>div:last-child>svg:first-child]:hidden [&[data-state=closed]>div>div:last-child>svg:last-child]:hidden">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-4 flex-1 min-w-0">
                    <span
                      className={`text-sm font-medium ${company.isArchived ? 'text-gray-500 line-through' : 'text-gray-900'} truncate flex items-center gap-1`}
                    >
                      🏢 {company.name}
                    </span>
                    <span
                      className={`text-xs text-gray-600 ${company.isArchived ? 'line-through' : ''} flex items-center gap-1`}
                    >
                      📍 {company.city}, {company.province}
                    </span>
                    {company.phone && (
                      <span
                        className={`text-xs text-gray-600 ${company.isArchived ? 'line-through' : ''} flex items-center gap-1`}
                      >
                        📞 {company.phone}
                      </span>
                    )}
                    {company.isArchived && (
                      <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                        Archived
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    <div
                      className="flex items-center gap-1"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Link
                        href={`/dashboard/setup/company-contact/show-company/${company.id}`}
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-6 w-6 p-0"
                        >
                          <Edit2 className="h-3 w-3" />
                        </Button>
                      </Link>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          modal(
                            ({ close }) => (
                              <MutationConfirmModal
                                close={close}
                                title="Delete Company"
                                description={`Are you sure you want to delete "${company.name}"?`}
                                mutateFn={() => CompanyQuery.delete(company.id)}
                                mutationKey={[...CompanyQuery.tags]}
                                onSuccess={async () => {
                                  await getQueryClient().invalidateQueries({
                                    queryKey: [
                                      ...CompanyQuery.tags,
                                      'Show manager',
                                    ],
                                  });
                                }}
                                danger
                              />
                            ),
                            DEFAULT_MODAL,
                          ).open();
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </AccordionTrigger>

              <AccordionContent className="px-0 pb-0">
                <div className="px-6 py-3 bg-brand-brown/5 border-t border-brand-brown/20">
                  <ContactsTable
                    companyId={company.id}
                    companyName={company.name}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
};

export default CompanyTable;

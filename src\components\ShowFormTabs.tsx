'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  GeneralInfoTab,
  HallDetailsTab,
  PromoterInfoTab,
  ShowScheduleTab,
  DatesTimesTab,
  OptionsSettingsTab,
  BoothOptionsTab,
  ProductsTab,
  ServicesTab,
  DocumentsTab,
  ReviewSubmitTab,
} from '@/components/show-tabs';

const steps = [
  'Setup New Show',
  'Hall Details',
  'Promoter Info',
  'Show Schedule',
  'Dates & Times',
  'Options & Settings',
  'Booth Options',
  'Products',
  'Services',
  'Documents',
  'Review & Submit',
];

interface ShowFormTabsProps {
  showId?: number;
}

export default function ShowFormTabs({ showId }: ShowFormTabsProps) {
  const router = useRouter();
  const isEditMode = !!showId;
  const [currentStep, setCurrentStep] = useState(0);

  const handleCancel = () => {
    router.push('/dashboard/setup/list-of-shows');
  };

  const handleSuccess = () => {
    router.push('/dashboard/setup/list-of-shows');
  };

  const renderCurrentTab = () => {
    switch (currentStep) {
      case 0:
        return <GeneralInfoTab showId={showId} onSuccess={handleSuccess} />;
      case 1:
        return <HallDetailsTab showId={showId} onSuccess={handleSuccess} />;
      case 2:
        return <PromoterInfoTab showId={showId} onSuccess={handleSuccess} />;
      case 3:
        return <ShowScheduleTab showId={showId} onSuccess={handleSuccess} />;
      case 4:
        return <DatesTimesTab showId={showId} onSuccess={handleSuccess} />;
      case 5:
        return <OptionsSettingsTab showId={showId} onSuccess={handleSuccess} />;
      case 6:
        return <BoothOptionsTab showId={showId} onSuccess={handleSuccess} />;
      case 7:
        return <ProductsTab showId={showId} onSuccess={handleSuccess} />;
      case 8:
        return <ServicesTab showId={showId} onSuccess={handleSuccess} />;
      case 9:
        return <DocumentsTab showId={showId} onSuccess={handleSuccess} />;
      case 10:
        return <ReviewSubmitTab showId={showId} onSuccess={handleSuccess} />;
      default:
        return <GeneralInfoTab showId={showId} onSuccess={handleSuccess} />;
    }
  };

  return (
    <div>
      <div className="mb-4">
        <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
          {isEditMode ? 'EDIT SHOW' : 'SETUP NEW SHOW'}
        </h1>
        <p className="text-slate-600 text-sm">
          Please complete the show's information. Required information is marked
          with a <span className="text-red-500">*</span>.
          <br />
          Form Buttons are located at the Bottom of the Form.
        </p>
      </div>

      <div className="flex">
        {/* Vertical tabs */}
        <div className="w-48 shrink-0 border-r border-slate-200 pr-4 mr-6">
          <div className="space-y-1">
            {steps.map((step, index) => (
              <button
                key={index}
                className={`w-full text-left px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  index === currentStep
                    ? 'bg-[#00646C]/10 text-[#00646C] border-l-2 border-[#00646C]'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
                onClick={() => setCurrentStep(index)}
              >
                {step}
              </button>
            ))}
          </div>
        </div>

        {/* Form content */}
        <div className="flex-1">
          {renderCurrentTab()}

          <div className="flex justify-between mt-8 pt-6 border-t border-slate-200">
            <Button
              variant="outline"
              className="border-slate-200"
              onClick={handleCancel}
            >
              Cancel
            </Button>

            <div className="flex gap-2">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  className="border-slate-200"
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Button
                  className="bg-[#00646C] hover:bg-[#00646C]/90"
                  onClick={() => setCurrentStep(currentStep + 1)}
                >
                  Next
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import ShowQuery from '@/services/queries/ShowQuery';
import { Spinner } from '@/components/ui/spinner';
import { ChevronLeft } from 'lucide-react';
import {
  GeneralInfoTab,
  HallContactTab,
  PromoterInfoTab,
  ShowScheduleTab,
  DatesTimesTab,
  OptionsSettingsTab,
  BoothOptionsTab,
  ProductsTab,
  ServicesTab,
  DocumentsTab,
  ReviewSubmitTab,
} from '@/components/show-tabs';

const steps = [
  'Setup New Show',
  'Hall & Contact',
  'Show Schedule',
  'Promoter Info',
  'Dates & Times',
  'Options & Settings',
  'Booth Options',
  'Products',
  'Services',
  'Documents',
  'Review & Submit',
];

interface ShowFormTabsProps {
  showId?: number;
}

export default function ShowFormTabs({ showId }: ShowFormTabsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const isEditMode = !!showId;

  // Get initial tab from URL or default to 0
  const getInitialTab = () => {
    const tabParam = searchParams.get('tab');
    if (tabParam) {
      const tabIndex = parseInt(tabParam, 10);
      return !isNaN(tabIndex) && tabIndex >= 0 && tabIndex < steps.length
        ? tabIndex
        : 0;
    }
    return 0;
  };

  const [currentStep, setCurrentStep] = useState(getInitialTab);
  const [createdShowId, setCreatedShowId] = useState<number | null>(null);

  // Update URL when tab changes
  const updateTabInUrl = (tabIndex: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tabIndex.toString());
    router.replace(url.pathname + url.search, { scroll: false });
  };

  // Update current step and URL
  const changeTab = (newStep: number) => {
    setCurrentStep(newStep);
    updateTabInUrl(newStep);
  };

  const { data: showData, isLoading: isLoadingShow } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: isEditMode,
  });

  const handleCancel = () => {
    router.push('/dashboard/setup/list-of-shows');
  };

  const handleTabSuccess = async (newShowId?: number) => {
    if (currentStep === 0 && !isEditMode && newShowId) {
      // First tab in add mode - navigate to edit mode with the new show ID
      router.push(`/dashboard/setup/list-of-shows/${newShowId}?tab=1`);
    } else if (currentStep < steps.length - 1) {
      // Move to next tab - invalidate current show data to ensure fresh data
      if (showId || newShowId) {
        await queryClient.invalidateQueries({
          queryKey: ['Shows', showId || newShowId],
        });
      }
      changeTab(currentStep + 1);
    } else {
      // Last tab - go back to list
      router.push('/dashboard/setup/list-of-shows');
    }
  };

  const handleFinalSuccess = () => {
    router.push('/dashboard/setup/list-of-shows');
  };

  const currentShowId = showId || (createdShowId ?? undefined);

  const renderCurrentTab = () => {
    switch (currentStep) {
      case 0:
        return <GeneralInfoTab showId={showId} onSuccess={handleTabSuccess} />;
      case 1:
        return (
          <HallContactTab showId={currentShowId} onSuccess={handleTabSuccess} />
        );
      case 2:
        return (
          <ShowScheduleTab
            showId={currentShowId}
            onSuccess={handleTabSuccess}
          />
        );
      case 3:
        return (
          <PromoterInfoTab
            showId={currentShowId}
            onSuccess={handleTabSuccess}
          />
        );
      case 4:
        return (
          <DatesTimesTab showId={currentShowId} onSuccess={handleTabSuccess} />
        );
      case 5:
        return (
          <OptionsSettingsTab
            showId={currentShowId}
            onSuccess={handleTabSuccess}
          />
        );
      case 6:
        return (
          <BoothOptionsTab
            showId={currentShowId}
            onSuccess={handleTabSuccess}
          />
        );
      case 7:
        return (
          <ProductsTab showId={currentShowId} onSuccess={handleTabSuccess} />
        );
      case 8:
        return (
          <ServicesTab showId={currentShowId} onSuccess={handleTabSuccess} />
        );
      case 9:
        return (
          <DocumentsTab showId={currentShowId} onSuccess={handleTabSuccess} />
        );
      case 10:
        return (
          <ReviewSubmitTab
            showId={currentShowId}
            onSuccess={handleFinalSuccess}
          />
        );
      default:
        return <GeneralInfoTab showId={showId} onSuccess={handleTabSuccess} />;
    }
  };

  const getTitle = () => {
    if (isEditMode && showData) {
      return showData.name || 'EDIT SHOW';
    }
    return 'SETUP NEW SHOW';
  };

  if (isLoadingShow && isEditMode) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4">
        <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
          {getTitle()}
        </h1>
        <p className="text-slate-600 text-sm">
          Please complete the show's information. Required information is marked
          with a <span className="text-red-500">*</span>.
          <br />
          Form Buttons are located at the Bottom of the Form.
        </p>
      </div>

      <div className="flex">
        {/* Vertical tabs */}
        <div className="w-48 shrink-0 border-r border-slate-200 pr-4 mr-6">
          <div className="space-y-1">
            {steps.map((step, index) => {
              const isDisabled = !isEditMode && index > 0; // Disable tabs after first one in add mode
              const isActive = index === currentStep;

              return (
                <button
                  key={index}
                  disabled={isDisabled}
                  className={`w-full text-left px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-[#00646C]/10 text-[#00646C] border-l-2 border-[#00646C]'
                      : isDisabled
                        ? 'text-slate-400 cursor-not-allowed'
                        : 'text-slate-600 hover:bg-slate-100'
                  }`}
                  onClick={async () => {
                    if (!isDisabled) {
                      // Invalidate show data when manually switching tabs to ensure fresh data
                      if (showId && index !== currentStep) {
                        await queryClient.invalidateQueries({
                          queryKey: ['Shows', showId],
                        });
                      }
                      changeTab(index);
                    }
                  }}
                >
                  {step}
                </button>
              );
            })}
          </div>
        </div>

        {/* Form content */}
        <div className="flex-1">
          {renderCurrentTab()}

          <div className="flex justify-between mt-8 pt-6 border-t border-slate-200">
            <Button
              variant="outline"
              className="border-slate-200"
              onClick={handleCancel}
            >
              Cancel
            </Button>

            <div className="flex gap-2">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  className="border-slate-200"
                  onClick={() => changeTab(currentStep - 1)}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, Edit2, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';

import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';

import ShowHallQuery from '@/services/queries/ShowHallQuery';
import { ShowHallData } from '@/schema/ShowHallSchema'; // TS type for hall data
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import AddHallModal from '../add_hall_modal';
import { Hall } from '@/models/ShowLocation';

interface ShowHallTableProps {
  locationId: number;
}

export const ShowHallTable = ({ locationId }: ShowHallTableProps) => {
  const { data, isLoading } = useQuery({
    queryKey: ['ShowHall', { location: Number(locationId) }],
    queryFn: () => ShowHallQuery.getByLocation(locationId),
    enabled: !!locationId,
  });

  const columns = generateTableColumns<Hall>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      hallName: { name: 'Name', type: 'text', sortable: true },
      hallCode: { name: 'Code', type: 'text', sortable: true },
      hallStyle: { name: 'Style', type: 'text', sortable: true },
      hallFloorType: { name: 'Floor Type', type: 'text', sortable: true },
      banquetCapacity: { name: 'Banquet Cap.', type: 'text', sortable: true },
      isArchived: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">Inactive</span>
                </>
              ) : (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Active</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                variant="secondary"
                size="icon"
                onClick={() => {
                  modal(
                    <AddHallModal hallId={row.id} locationId={locationId} />,
                    {
                      ...DEFAULT_MODAL,
                      width: '60%',
                    },
                  ).open();
                }}
              >
                <Edit2 className="size-4" />
              </Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowHallData>({
    hallName: {
      name: 'Name',
      type: 'text',
    },
    isArchived: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'false' },
          { label: 'Inactive', value: 'true' },
        ],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      filterFields={filters}
      data={data}
      isLoading={isLoading}
      controls={
        <div className="flex justify-end gap-2">
          <Button
            variant="main"
            onClick={() => {
              modal(<AddHallModal locationId={locationId} />, {
                ...DEFAULT_MODAL,
                width: '60%',
              }).open();
            }}
          >
            <FaPlus />
            Add New Hall
          </Button>
        </div>
      }
    />
  );
};

export default ShowHallTable;

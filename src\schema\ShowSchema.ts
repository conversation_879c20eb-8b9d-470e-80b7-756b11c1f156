import { z } from 'zod';

export const ShowCreateSchema = z.object({
  name: z.string().min(1, { message: 'Show name is required' }),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().min(1, { message: 'End date is required' }),
  displayDate: z.string().min(1, { message: 'Display date is required' }),
  orderDeadlineDate: z.string().min(1, { message: 'Order deadline date is required' }),
  lateChargePercentage: z.string().min(1, { message: 'Late charge percentage is required' }),
  link: z
    .string()
    .url({ message: 'Please enter a valid URL' })
    .optional()
    .or(z.literal('')),
  description: z.string().optional(),
  display: z.boolean().default(true),
  locationId: z
    .string()
    .min(1, { message: 'Location is required' })
    .transform((val) => Number(val)),
  kioskPrintingQueueDate: z.string().optional(),
  view: z.boolean().default(true),
});

export const ShowUpdateSchema = z.object({
  name: z.string().min(1, { message: 'Show name is required' }),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().min(1, { message: 'End date is required' }),
  displayDate: z.string().min(1, { message: 'Display date is required' }),
  orderDeadlineDate: z.string().min(1, { message: 'Order deadline date is required' }),
  lateChargePercentage: z.string().min(1, { message: 'Late charge percentage is required' }),
  link: z
    .string()
    .url({ message: 'Please enter a valid URL' })
    .optional()
    .or(z.literal('')),
  description: z.string().optional(),
  display: z.boolean(),
  locationId: z
    .string()
    .min(1, { message: 'Location is required' })
    .transform((val) => Number(val)),
  kioskPrintingQueueDate: z.string().optional(),
  view: z.boolean(),
});

export type ShowCreateData = z.infer<typeof ShowCreateSchema>;
export type ShowUpdateData = z.infer<typeof ShowUpdateSchema>;

'use client';

import { Button } from '@/components/ui/button';
import { PlusCircle, Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Input } from '@/components/ui/inputs/input';
import MenuItemCard from './menu_item_card';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { getQueryClient } from '@/utils/query-client';
import Link from 'next/link';
import MenuQuery from '@/services/queries/MenuQuery';
import { MenuItem } from '@/models/MenuItem';

export default function MenuItemManagement({ data }: { data: MenuItem[] }) {
  const [search, setSearch] = useState('');

  // Filter items based on search input
  const filteredItems = useMemo(() => {
    return (data ?? []).filter((item) =>
      item.name.toLowerCase().includes(search.toLowerCase()),
    );
  }, [search, data]);

  // Organize items into a tree structure
  const buildTree = (
    items: MenuItem[],
    parentId: number | null = null,
  ): MenuItem[] => {
    return items
      .filter((item) => item.parent?.id === parentId)
      .map((item) => ({
        ...item,
        children: buildTree(items, item.id), // Recursively find children
      }));
  };

  const organizedItems = useMemo(
    () => filteredItems.filter((i) => !i.parent),
    [filteredItems],
  );

  // Render menu items recursively
  const renderMenuItems = (items: MenuItem[], depth = 0) => {
    return items
      .sort((a, b) => a.displayOrder - b.displayOrder)
      .map((item, index) => (
        <div key={item.id} className="space-y-2 pl-[calc(1rem*depth)]">
          <MenuItemCard
            item={item}
            depth={depth}
            isLast={index === items.length - 1}
            isFirst={index === 0}
          />
          {item.children && renderMenuItems(item.children, depth + 1)}
        </div>
      ));
  };
  const { toast } = useToast();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: MenuQuery.create,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [...MenuQuery.tags],
      });

      toast({
        variant: 'default',
        title: 'Created Successfully',
      });
    },
  });
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search menu items..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-8"
          />
        </div>
        <Link href={`/dashboard/setup/system-setup/menu-management/add`}>
          <Button variant={'main'}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add new Item
          </Button>
        </Link>
      </div>
      <div className="space-y-4">{renderMenuItems(organizedItems)}</div>
    </div>
  );
}

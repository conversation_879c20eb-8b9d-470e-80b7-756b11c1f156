'use client';

import {
  <PERSON><PERSON><PERSON>,
  Arrow<PERSON>p,
  BarChart3,
  CheckCircle2,
  Clock,
  DollarSign,
  FileText,
  MoreHorizontal,
  Plus,
  ShoppingCart,
  Users,
  ChevronDown,
  ChevronUp,
  Filter,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useState } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from './ui/Collapsible';
import { Badge } from './ui/Badge';

interface DashboardContentProps {
  currentRole: string;
}

export function DashboardContent({ currentRole }: DashboardContentProps) {
  const [expandedKPIs, setExpandedKPIs] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState(true);

  const roleContent = {
    finance: {
      title: 'Finance Manager Role Center',
      kpis: [
        {
          title: 'Total Revenue',
          value: '$124,563.00',
          change: '+12.5%',
          icon: DollarSign,
          trend: 'up',
          progress: 75,
        },
        {
          title: 'Expenses',
          value: '$54,283.00',
          change: '-2.3%',
          icon: FileText,
          trend: 'down',
          progress: 45,
        },
        {
          title: 'Cash Flow',
          value: '$70,280.00',
          change: '+18.7%',
          icon: BarChart3,
          trend: 'up',
          progress: 65,
        },
        {
          title: 'Outstanding Invoices',
          value: '$32,583.00',
          change: '-5.1%',
          icon: FileText,
          trend: 'down',
          progress: 30,
        },
      ],
      tasks: [
        {
          title: 'Review monthly financial statements',
          priority: 'high',
          due: 'Today',
          complete: false,
        },
        {
          title: 'Approve vendor payments',
          priority: 'medium',
          due: 'Tomorrow',
          complete: false,
        },
        {
          title: 'Reconcile bank accounts',
          priority: 'medium',
          due: 'In 2 days',
          complete: false,
        },
        {
          title: 'Prepare tax documentation',
          priority: 'high',
          due: 'Next week',
          complete: false,
        },
        {
          title: 'Budget planning for Q3',
          priority: 'low',
          due: 'Next week',
          complete: false,
        },
      ],
      activities: [
        {
          user: 'Sarah Johnson',
          action: 'approved expense report #4582',
          time: '10 minutes ago',
        },
        {
          user: 'Michael Chen',
          action: 'created new budget for Marketing dept',
          time: '1 hour ago',
        },
        {
          user: 'Emma Davis',
          action: 'reconciled bank statement for April',
          time: '3 hours ago',
        },
        {
          user: 'Robert Wilson',
          action: 'submitted quarterly tax report',
          time: 'Yesterday',
        },
        {
          user: 'Jennifer Lee',
          action: 'updated financial forecast',
          time: '2 days ago',
        },
      ],
    },
    sales: {
      title: 'Sales Manager Role Center',
      kpis: [
        {
          title: 'Total Sales',
          value: '$87,954.00',
          change: '+8.3%',
          icon: ShoppingCart,
          trend: 'up',
          progress: 83,
        },
        {
          title: 'New Customers',
          value: '124',
          change: '+15.2%',
          icon: Users,
          trend: 'up',
          progress: 68,
        },
        {
          title: 'Conversion Rate',
          value: '24.8%',
          change: '+3.5%',
          icon: BarChart3,
          trend: 'up',
          progress: 55,
        },
        {
          title: 'Average Order Value',
          value: '$1,283.00',
          change: '-1.2%',
          icon: ShoppingCart,
          trend: 'down',
          progress: 42,
        },
      ],
      tasks: [
        {
          title: 'Follow up with Enterprise leads',
          priority: 'high',
          due: 'Today',
          complete: false,
        },
        {
          title: 'Prepare quarterly sales report',
          priority: 'medium',
          due: 'Tomorrow',
          complete: true,
        },
        {
          title: 'Review sales team performance',
          priority: 'medium',
          due: 'In 2 days',
          complete: false,
        },
        {
          title: 'Update sales forecast',
          priority: 'high',
          due: 'Next week',
          complete: false,
        },
        {
          title: 'Plan sales team meeting',
          priority: 'low',
          due: 'Next week',
          complete: false,
        },
      ],
      activities: [
        {
          user: 'David Smith',
          action: 'closed deal with Acme Corp worth $25,000',
          time: '30 minutes ago',
        },
        {
          user: 'Lisa Brown',
          action: 'created new sales quote for XYZ Inc',
          time: '2 hours ago',
        },
        {
          user: 'James Taylor',
          action: 'added 15 new leads from trade show',
          time: '4 hours ago',
        },
        {
          user: 'Patricia Moore',
          action: 'updated sales pipeline forecast',
          time: 'Yesterday',
        },
        {
          user: 'Richard Martin',
          action: 'achieved monthly sales target',
          time: '2 days ago',
        },
      ],
    },
    inventory: {
      title: 'Inventory Manager Role Center',
      kpis: [
        {
          title: 'Total Items',
          value: '2,583',
          change: '+3.1%',
          icon: ShoppingCart,
          trend: 'up',
          progress: 60,
        },
        {
          title: 'Low Stock Items',
          value: '47',
          change: '-12.5%',
          icon: ShoppingCart,
          trend: 'down',
          progress: 25,
        },
        {
          title: 'Pending Orders',
          value: '124',
          change: '+8.7%',
          icon: Clock,
          trend: 'up',
          progress: 70,
        },
        {
          title: 'Warehouse Capacity',
          value: '78.3%',
          change: '+2.4%',
          icon: BarChart3,
          trend: 'up',
          progress: 78,
        },
      ],
      tasks: [
        {
          title: 'Process incoming shipment',
          priority: 'high',
          due: 'Today',
          complete: false,
        },
        {
          title: 'Reorder low stock items',
          priority: 'high',
          due: 'Today',
          complete: false,
        },
        {
          title: 'Conduct inventory count in Warehouse B',
          priority: 'medium',
          due: 'Tomorrow',
          complete: false,
        },
        {
          title: 'Update inventory valuation',
          priority: 'medium',
          due: 'In 3 days',
          complete: true,
        },
        {
          title: 'Review supplier performance',
          priority: 'low',
          due: 'Next week',
          complete: false,
        },
      ],
      activities: [
        {
          user: 'Thomas Anderson',
          action: 'received shipment #4582 from Supplier Inc',
          time: '45 minutes ago',
        },
        {
          user: 'Nancy White',
          action: 'transferred 50 units from Warehouse A to B',
          time: '2 hours ago',
        },
        {
          user: 'Steven Clark',
          action: 'updated stock levels after inventory count',
          time: '5 hours ago',
        },
        {
          user: 'Karen Lewis',
          action: 'processed return from customer #1205',
          time: 'Yesterday',
        },
        {
          user: 'Daniel Walker',
          action: 'created purchase order for 200 units',
          time: '2 days ago',
        },
      ],
    },
    admin: {
      title: 'Administrator Role Center',
      kpis: [
        {
          title: 'Active Users',
          value: '124',
          change: '+5.1%',
          icon: Users,
          trend: 'up',
          progress: 85,
        },
        {
          title: 'System Uptime',
          value: '99.98%',
          change: '+0.1%',
          icon: CheckCircle2,
          trend: 'up',
          progress: 99,
        },
        {
          title: 'Pending Approvals',
          value: '12',
          change: '-25.0%',
          icon: Clock,
          trend: 'down',
          progress: 40,
        },
        {
          title: 'Open Support Tickets',
          value: '8',
          change: '-15.3%',
          icon: FileText,
          trend: 'down',
          progress: 35,
        },
      ],
      tasks: [
        {
          title: 'Review user access requests',
          priority: 'high',
          due: 'Today',
          complete: false,
        },
        {
          title: 'Schedule system maintenance',
          priority: 'medium',
          due: 'Tomorrow',
          complete: false,
        },
        {
          title: 'Update security policies',
          priority: 'high',
          due: 'In 2 days',
          complete: false,
        },
        {
          title: 'Review audit logs',
          priority: 'medium',
          due: 'Next week',
          complete: true,
        },
        {
          title: 'Plan user training session',
          priority: 'low',
          due: 'Next week',
          complete: false,
        },
      ],
      activities: [
        {
          user: 'Admin',
          action: 'granted access to new finance module for 5 users',
          time: '20 minutes ago',
        },
        {
          user: 'System',
          action: 'completed database backup successfully',
          time: '1 hour ago',
        },
        {
          user: 'Admin',
          action: 'updated company information',
          time: '3 hours ago',
        },
        {
          user: 'System',
          action: 'installed security patch KB45872',
          time: 'Yesterday',
        },
        {
          user: 'Admin',
          action: "created new role 'Regional Manager'",
          time: '2 days ago',
        },
      ],
    },
  };

  const content = roleContent[currentRole as keyof typeof roleContent];

  return (
    <TooltipProvider>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-medium text-slate-800">
            {content.title}
          </h1>
          <Button className="bg-emerald-600 hover:bg-emerald-700">
            <Plus className="mr-2 h-4 w-4" />
            New Action
          </Button>
        </div>

        <Collapsible
          open={expandedKPIs}
          onOpenChange={setExpandedKPIs}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-slate-700">
              Key Performance Indicators
            </h2>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="gap-1 text-slate-500 hover:text-slate-900"
              >
                {expandedKPIs ? (
                  <>
                    <ChevronUp className="h-4 w-4" />
                    <span className="sr-only md:not-sr-only text-sm">
                      Collapse
                    </span>
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4" />
                    <span className="sr-only md:not-sr-only text-sm">
                      Expand
                    </span>
                  </>
                )}
              </Button>
            </CollapsibleTrigger>
          </div>

          <CollapsibleContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {content.kpis.map((kpi, index) => (
                <Card
                  key={index}
                  className="overflow-hidden bg-white shadow-sm border-0"
                >
                  <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                    <CardTitle className="text-sm font-medium text-slate-700">
                      {kpi.title}
                    </CardTitle>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="rounded-full p-1 hover:bg-slate-100">
                          <kpi.icon className="h-4 w-4 text-slate-400" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>View {kpi.title} details</p>
                      </TooltipContent>
                    </Tooltip>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-slate-800">
                      {kpi.value}
                    </div>
                    <div className="mt-1 flex items-center justify-between">
                      <p className="text-xs flex items-center">
                        {kpi.trend === 'up' ? (
                          <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
                        ) : (
                          <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
                        )}
                        <span
                          className={
                            kpi.trend === 'up'
                              ? 'text-emerald-500'
                              : 'text-rose-500'
                          }
                        >
                          {kpi.change}
                        </span>
                      </p>
                      <span className="text-xs text-slate-500">
                        vs. last month
                      </span>
                    </div>
                    <div className="mt-3">
                      <Progress value={kpi.progress} className="h-1.5" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible
          open={expandedTasks}
          onOpenChange={setExpandedTasks}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-slate-700">My Workspace</h2>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="gap-1 text-slate-500 hover:text-slate-900"
              >
                {expandedTasks ? (
                  <>
                    <ChevronUp className="h-4 w-4" />
                    <span className="sr-only md:not-sr-only text-sm">
                      Collapse
                    </span>
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4" />
                    <span className="sr-only md:not-sr-only text-sm">
                      Expand
                    </span>
                  </>
                )}
              </Button>
            </CollapsibleTrigger>
          </div>

          <CollapsibleContent>
            <Tabs defaultValue="tasks" className="space-y-4">
              <div className="flex items-center justify-between">
                <TabsList className="bg-slate-100">
                  <TabsTrigger
                    value="tasks"
                    className="data-[state=active]:bg-white"
                  >
                    My Tasks
                  </TabsTrigger>
                  <TabsTrigger
                    value="activities"
                    className="data-[state=active]:bg-white"
                  >
                    Recent Activities
                  </TabsTrigger>
                </TabsList>

                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 text-slate-600"
                >
                  <Filter className="h-3.5 w-3.5" />
                  <span>Filter</span>
                </Button>
              </div>

              <TabsContent value="tasks" className="mt-0">
                <Card className="bg-white shadow-sm border-0">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-slate-800">Tasks</CardTitle>
                        <CardDescription>
                          Your pending tasks and approvals
                        </CardDescription>
                      </div>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="gap-1 text-slate-600"
                          >
                            <Plus className="h-3.5 w-3.5" />
                            <span>Add Task</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Add a new task (Ctrl+N)</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-[350px]">
                      <div>
                        {content.tasks.map((task, index) => (
                          <div
                            key={index}
                            className={`flex items-center justify-between p-4 hover:bg-slate-50 transition-colors ${
                              index !== content.tasks.length - 1
                                ? 'border-b border-slate-100'
                                : ''
                            } ${task.complete ? 'opacity-60' : ''}`}
                          >
                            <div className="flex items-start gap-4">
                              <div className="pt-1">
                                <div
                                  className={`
                                  flex h-6 w-6 items-center justify-center rounded-full border 
                                  ${
                                    task.complete
                                      ? 'bg-emerald-500 border-emerald-500 text-white'
                                      : 'border-slate-300 bg-white'
                                  }
                                `}
                                >
                                  {task.complete && (
                                    <CheckCircle2 className="h-4 w-4" />
                                  )}
                                </div>
                              </div>
                              <div>
                                <p
                                  className={`text-sm font-medium leading-none ${
                                    task.complete
                                      ? 'line-through text-slate-500'
                                      : 'text-slate-800'
                                  }`}
                                >
                                  {task.title}
                                </p>
                                <p className="text-sm text-slate-500 mt-1">
                                  Due: {task.due}
                                </p>
                              </div>
                            </div>
                            <Badge
                              variant="outline"
                              className={
                                task.priority === 'high'
                                  ? 'bg-rose-50 text-rose-700 hover:bg-rose-50 border-rose-200'
                                  : task.priority === 'medium'
                                    ? 'bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200'
                                    : 'bg-emerald-50 text-emerald-700 hover:bg-emerald-50 border-emerald-200'
                              }
                            >
                              {task.priority}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="activities" className="mt-0">
                <Card className="bg-white shadow-sm border-0">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-slate-800">
                          Recent Activities
                        </CardTitle>
                        <CardDescription>
                          Latest actions and updates
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-[350px]">
                      <div>
                        {content.activities.map((activity, index) => (
                          <div
                            key={index}
                            className={`flex items-start gap-4 p-4 hover:bg-slate-50 transition-colors ${
                              index !== content.activities.length - 1
                                ? 'border-b border-slate-100'
                                : ''
                            }`}
                          >
                            <Avatar className="h-9 w-9">
                              <AvatarImage
                                src={`/abstract-geometric-shapes.png?height=36&width=36&query=${activity.user}`}
                                alt={activity.user}
                              />
                              <AvatarFallback>
                                {activity.user.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 space-y-1">
                              <p className="text-sm leading-none">
                                <span className="font-medium text-slate-800">
                                  {activity.user}
                                </span>{' '}
                                <span className="text-slate-600">
                                  {activity.action}
                                </span>
                              </p>
                              <p className="text-xs text-slate-500">
                                {activity.time}
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-slate-400 hover:text-slate-600"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">More</span>
                            </Button>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                  <CardFooter className="border-t border-slate-100 p-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full text-slate-600"
                    >
                      View All Activities
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </TooltipProvider>
  );
}

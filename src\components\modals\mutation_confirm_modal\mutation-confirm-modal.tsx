import { useMutation } from '@tanstack/react-query';

import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
interface IMutationConfirmModal<T> {
  close: () => void;
  mutateFn: () => Promise<T>;
  mutationKey: any[];
  onSuccess?: (data: T) => void;
  onError?: () => void;
  title: string;
  description: string;
  danger?: boolean;
}
export default function MutationConfirmModal<T>({
  close,
  description,
  title,
  onSuccess,
  mutateFn,
  danger,
  onError,
  mutationKey,
}: IMutationConfirmModal<T>) {
  const { mutate, isPending } = useMutation({
    mutationKey: mutationKey,
    mutationFn: mutateFn,
    onSuccess: async (data) => {
      onSuccess?.(data);
      close();
    },
    onError: () => {
      onError?.();
    },
  });
  return (
    <ModalContainer
      onSubmit={(e) => {
        e.preventDefault();
        mutate();
      }}
      className="gap-0"
      title={title}
      description={description}
      controls={
        <div className="flex flex-row gap-2">
          <Button
            disabled={isPending}
            type="submit"
            className={cn({
              'bg-green-500': !danger,
              'bg-destructive': danger,
            })}
          >
            {isPending ? 'Please wait...' : 'Confirm'}
          </Button>
          <Button onClick={close} variant="secondary" disabled={isPending}>
            Cancel
          </Button>
        </div>
      }
    />
  );
}

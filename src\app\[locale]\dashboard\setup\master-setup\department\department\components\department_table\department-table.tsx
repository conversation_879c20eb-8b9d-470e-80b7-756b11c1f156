'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import DepartmentQuery from '@/services/queries/DepartmentQuery';
import { Button } from '@/components/ui/button';
import DepartmentModal from '../department_modal';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { BriefData } from '@/models/BriefData';

export const DepartmentTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: DepartmentQuery.tags,
    queryFn: DepartmentQuery.getAll,
  });

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries, isLoading: isLoadingContries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const columns = generateTableColumns<BriefData>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full justify-center">
              <Button
                variant="secondary"
                size="icon"
                onClick={() => {
                  modal(<DepartmentModal id={row.id} />, DEFAULT_MODAL).open();
                }}
              >
                <Edit2 className="size-4" />
              </Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<BriefData>({
    name: {
      name: 'Name',
      type: 'text',
    },
  });

  return (
    <DataTable
      filterFields={filters}
      columns={columns}
      data={data}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Button
            variant="main"
            onClick={() => {
              modal(<DepartmentModal />, {
                ...DEFAULT_MODAL,
                width: '40%',
              }).open();
            }}
          >
            <FaPlus />
            Add New Department
          </Button>
        </div>
      }
    />
  );
};

export default DepartmentTable;

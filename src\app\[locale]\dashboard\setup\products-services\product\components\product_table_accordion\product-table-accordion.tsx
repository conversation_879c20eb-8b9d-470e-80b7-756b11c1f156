'use client';

import { useQuery } from '@tanstack/react-query';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Spinner } from '@/components/ui/spinner';
import CategoryDisplay from '../category_display';

export const ProductTableAccordion = () => {
  const groupId = 2;
  const { data, isLoading } = useQuery({
    queryKey: GroupTypeQuery.tags,
    queryFn: () => GroupTypeQuery.getAllByGroupIdHierarchical(groupId),
  });

  if (isLoading)
    return (
      <div>
        <Spinner />
      </div>
    );

  return (
    <div>
      {data &&
        data?.group.map((g) => (
          <Accordion
            key={g.groupId}
            type="single"
            collapsible
            className="space-y-3"
          >
            <AccordionItem value={g.groupId.toString()}>
              <AccordionTrigger
                className={`px-3 py-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg w-full`}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-4 flex-1 min-w-0 hover:text-main hover:underline">
                    <span
                      className={`text-md font-medium ${g.categories.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 ${!g.isAvailable && 'line-through'}`}
                    >
                      {g.groupName}
                    </span>
                    {/* {g.categories.length === 0 && (
                      <span className="text-xs text-gray-600 line-through">
                        No category available
                      </span>
                    )} */}
                  </div>

                  {/* <Link
                    href={`/dashboard/setup/product/${groupId}/category/${category.categoryId}/add`}
                  >
                    <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                      <FaPlus className="h-3 w-3" />
                    </Button>
                  </Link> */}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-3">
                {g.categories && g.categories.length > 0 ? (
                  g.categories.map((c) => (
                    <CategoryDisplay
                      key={c.categoryId}
                      data={c}
                      groupId={groupId}
                      categoryId={c.categoryId}
                    />
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">
                    No category in this group.
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ))}
    </div>
  );
};

export default ProductTableAccordion;

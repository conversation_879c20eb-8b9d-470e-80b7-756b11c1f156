'use client';

import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { format, parseISO, isValid } from 'date-fns';

import { getQueryClient } from '@/utils/query-client';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';

import ShowQuery from '@/services/queries/ShowQuery';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { Show } from '@/models/Show';

interface GeneralInfoTabProps {
  showId?: number;
  onSuccess?: (newShowId?: number) => void;
}

function FormContent({
  defaultValues,
  showId,
  onSuccess,
}: {
  defaultValues?: any;
  showId?: number;
  onSuccess?: (newShowId?: number) => void;
}) {
  const { toast } = useToast();
  const isEditMode = !!showId;

  const { data: locations } = useQuery({
    queryKey: ShowLocationQuery.tags,
    queryFn: ShowLocationQuery.getAll,
  });

  const form = useForm<any>({
    defaultValues: defaultValues ?? {
      name: '',
      startDate: '',
      endDate: '',
      displayDate: '',
      orderDeadlineDate: '',
      lateChargePercentage: '0',
      link: '',
      description: '',
      display: true,
      locationId: '',
      kioskPrintingQueueDate: '',
      view: true,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: any) => {
      // Helper function to format dates properly using date-fns
      const formatDate = (dateValue: any, time: string) => {
        if (!dateValue) return '';

        try {
          let date: Date;

          // Handle different input types
          if (dateValue instanceof Date) {
            date = dateValue;
          } else if (typeof dateValue === 'string') {
            // Try to parse ISO string first, then fallback to new Date()
            date = dateValue.includes('T')
              ? parseISO(dateValue)
              : new Date(dateValue + 'T00:00:00');
          } else {
            return '';
          }

          // Validate the date
          if (!isValid(date)) {
            return '';
          }

          // Format to YYYY-MM-DD and append time
          return format(date, 'yyyy-MM-dd') + time;
        } catch (error) {
          return '';
        }
      };

      const formattedData = {
        ...data,
        locationId: Number(data.locationId),
        startDate: formatDate(data.startDate, 'T23:59:59'),
        endDate: formatDate(data.endDate, 'T23:59:59'),
        displayDate: formatDate(data.displayDate, 'T23:59:59'),
        orderDeadlineDate: formatDate(data.orderDeadlineDate, 'T23:59:59'),
        kioskPrintingQueueDate: formatDate(
          data.kioskPrintingQueueDate,
          'T23:59:59',
        ),
        link: data.link || '',
        description: data.description || '',
      };

      if (isEditMode) {
        await ShowQuery.update(showId!)(formattedData);
      } else {
        return ShowQuery.create(formattedData);
      }
    },
    onSuccess: async (newShowId?: number) => {
      await getQueryClient().invalidateQueries({
        queryKey: ShowQuery.tags,
      });

      if (showId) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Shows', showId],
        });
      }

      toast({
        title: 'Success',
        description: isEditMode
          ? 'Show updated successfully'
          : 'Show created successfully',
      });

      onSuccess?.(newShowId);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save show',
        variant: 'destructive',
      });
    },
  });

  const locationOptions =
    locations?.map((location) => ({
      label: location.name || `Location ${location.id}`,
      value: location.id.toString(),
    })) || [];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              General Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Field
                control={form.control}
                name="name"
                label="Show Name"
                type="text"
                required
              />

              <Field
                control={form.control}
                name="locationId"
                label="Location"
                type={{
                  type: 'select',
                  props: {
                    options: locationOptions,
                    placeholder:
                      locationOptions.length > 0
                        ? 'Select Location'
                        : 'No locations available',
                  },
                }}
                required
              />

              <Field
                control={form.control}
                name="startDate"
                label="Start Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="endDate"
                label="End Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="displayDate"
                label="Display Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="orderDeadlineDate"
                label="Order Deadline Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="lateChargePercentage"
                label="Late Charge Percentage"
                type="number"
                required
              />

              <Field
                control={form.control}
                name="kioskPrintingQueueDate"
                label="Kiosk Printing Queue Date"
                type="date"
              />

              <div className="md:col-span-2">
                <Field
                  control={form.control}
                  name="link"
                  label="Show Link"
                  type="text"
                />
              </div>

              <div className="md:col-span-2">
                <Field
                  control={form.control}
                  name="description"
                  label="Description"
                  type="textarea"
                />
              </div>

              <Field
                control={form.control}
                name="display"
                label="Display Show"
                type="checkbox"
              />

              <Field
                control={form.control}
                name="view"
                label="Show Visible"
                type="checkbox"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6 pt-4 border-t border-slate-200">
          <Button
            type="submit"
            className="bg-[#00646C] hover:bg-[#00646C]/90"
            disabled={isPending}
          >
            {isPending ? <Spinner className="mr-2" /> : null}
            {isPending
              ? 'Saving...'
              : isEditMode
                ? 'Update & Continue'
                : 'Save & Continue'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function GeneralInfoTab({
  showId,
  onSuccess,
}: GeneralInfoTabProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: !!showId,
    select: (res: Show) => ({
      name: res.name || '',
      startDate: res.startDate ? res.startDate.split('T')[0] : '',
      endDate: res.endDate ? res.endDate.split('T')[0] : '',
      displayDate: res.displayDate ? res.displayDate.split('T')[0] : '',
      orderDeadlineDate: res.orderDeadlineDate
        ? res.orderDeadlineDate.split('T')[0]
        : '',
      lateChargePercentage: res.lateChargePercentage || '0',
      link: res.link || '',
      description: res.description || '',
      display: res.display,
      locationId: res.locationId ? res.locationId.toString() : '',
      kioskPrintingQueueDate: res.kioskPrintingQueueDate
        ? res.kioskPrintingQueueDate.split('T')[0]
        : '',
      view: res.view,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} showId={showId} onSuccess={onSuccess} />
    </Suspense>
  );
}

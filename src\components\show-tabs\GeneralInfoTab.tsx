'use client';

import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import ShowQuery from '@/services/queries/ShowQuery';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';

interface GeneralInfoTabProps {
  showId?: number;
  onSuccess?: (newShowId?: number) => void;
}

export default function GeneralInfoTab({
  showId,
  onSuccess,
}: GeneralInfoTabProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const isEditMode = !!showId;

  const {
    data: locations,
    isLoading: isLoadingLocations,
    isSuccess: isLocationsSuccess,
  } = useQuery({
    queryKey: ShowLocationQuery.tags,
    queryFn: ShowLocationQuery.getAll,
  });

  const {
    data: showData,
    isLoading: isLoadingShow,
    isSuccess: isShowDataSuccess,
  } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: isEditMode,
  });

  const form = useForm({
    defaultValues: {
      name: '',
      startDate: '',
      endDate: '',
      displayDate: '',
      orderDeadlineDate: '',
      lateChargePercentage: '0',
      link: '',
      description: '',
      display: true,
      locationId: '',
      kioskPrintingQueueDate: '',
      view: true,
    },
  });

  useEffect(() => {
    if (
      isEditMode &&
      isShowDataSuccess &&
      isLocationsSuccess &&
      showData &&
      locations
    ) {
      // Only reset form when we have successfully loaded both show data and locations
      // This ensures the select component has options available when setting the value
      form.reset({
        name: showData.name || '',
        startDate: showData.startDate ? showData.startDate.split('T')[0] : '',
        endDate: showData.endDate ? showData.endDate.split('T')[0] : '',
        displayDate: showData.displayDate
          ? showData.displayDate.split('T')[0]
          : '',
        orderDeadlineDate: showData.orderDeadlineDate
          ? showData.orderDeadlineDate.split('T')[0]
          : '',
        lateChargePercentage: showData.lateChargePercentage || '0',
        link: showData.link || '',
        description: showData.description || '',
        display: showData.display,
        locationId: showData.locationId ? showData.locationId.toString() : '',
        kioskPrintingQueueDate: showData.kioskPrintingQueueDate
          ? showData.kioskPrintingQueueDate.split('T')[0]
          : '',
        view: showData.view,
      });
    }
  }, [
    isEditMode,
    isShowDataSuccess,
    isLocationsSuccess,
    showData,
    locations,
    form,
  ]);

  const createMutation = useMutation({
    mutationFn: ShowQuery.create,
    onSuccess: async (newShowId: number) => {
      toast({
        title: 'Success',
        description: 'Show created successfully',
      });

      // Invalidate all show-related queries
      await queryClient.invalidateQueries({ queryKey: ShowQuery.tags });

      // Pre-fetch the newly created show data for better UX
      await queryClient.prefetchQuery({
        queryKey: ['Shows', newShowId],
        queryFn: () => ShowQuery.getOne(newShowId),
      });

      onSuccess?.(newShowId);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create show',
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: ShowQuery.update(showId!),
    onSuccess: async () => {
      toast({
        title: 'Success',
        description: 'Show updated successfully',
      });

      // Invalidate all show-related queries
      await queryClient.invalidateQueries({ queryKey: ShowQuery.tags });

      // Invalidate the specific show query to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: ['Shows', showId] });

      // Refetch the updated show data immediately
      await queryClient.refetchQueries({ queryKey: ['Shows', showId] });

      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update show',
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: any) => {
    const formattedData = {
      ...data,
      locationId: Number(data.locationId),
      startDate: data.startDate + 'T00:00:00',
      endDate: data.endDate + 'T23:59:59',
      displayDate: data.displayDate + 'T00:00:00',
      orderDeadlineDate: data.orderDeadlineDate + 'T23:59:59',
      kioskPrintingQueueDate: data.kioskPrintingQueueDate
        ? data.kioskPrintingQueueDate + 'T00:00:00'
        : '',
      link: data.link || '',
      description: data.description || '',
    };

    if (isEditMode) {
      updateMutation.mutate(formattedData);
    } else {
      createMutation.mutate(formattedData);
    }
  };

  // Show loading spinner while data is loading
  if (isLoadingLocations || (isEditMode && isLoadingShow)) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  // Don't render form in edit mode until we have both show data and locations
  if (
    isEditMode &&
    (!isShowDataSuccess || !isLocationsSuccess || !showData || !locations)
  ) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  const locationOptions =
    locations?.map((location) => ({
      label: location.name || `Location ${location.id}`,
      value: location.id.toString(),
    })) || [];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              General Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Field
                control={form.control}
                name="name"
                label="Show Name"
                type="text"
                required
              />

              <Field
                control={form.control}
                name="locationId"
                label="Location"
                type={{
                  type: 'select',
                  props: {
                    options: locationOptions,
                    placeholder:
                      locationOptions.length > 0
                        ? 'Select Location'
                        : 'No locations available',
                  },
                }}
                required
              />

              <Field
                control={form.control}
                name="startDate"
                label="Start Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="endDate"
                label="End Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="displayDate"
                label="Display Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="orderDeadlineDate"
                label="Order Deadline Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="lateChargePercentage"
                label="Late Charge Percentage"
                type="number"
                required
              />

              <Field
                control={form.control}
                name="kioskPrintingQueueDate"
                label="Kiosk Printing Queue Date"
                type="date"
              />

              <div className="md:col-span-2">
                <Field
                  control={form.control}
                  name="link"
                  label="Show Link"
                  type="text"
                />
              </div>

              <div className="md:col-span-2">
                <Field
                  control={form.control}
                  name="description"
                  label="Description"
                  type="textarea"
                />
              </div>

              <Field
                control={form.control}
                name="display"
                label="Display Show"
                type="checkbox"
              />

              <Field
                control={form.control}
                name="view"
                label="Show Visible"
                type="checkbox"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6 pt-4 border-t border-slate-200">
          <Button
            type="submit"
            className="bg-[#00646C] hover:bg-[#00646C]/90"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {createMutation.isPending || updateMutation.isPending
              ? 'Saving...'
              : isEditMode
                ? 'Update & Continue'
                : 'Save & Continue'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

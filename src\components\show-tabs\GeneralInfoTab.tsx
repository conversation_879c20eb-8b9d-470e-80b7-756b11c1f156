'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { format, parseISO, isValid, isAfter, isBefore } from 'date-fns';
import { z } from 'zod';

import { getQueryClient } from '@/utils/query-client';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';

import ShowQuery from '@/services/queries/ShowQuery';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { Show } from '@/models/Show';

interface GeneralInfoTabProps {
  showId?: number;
  onSuccess?: (newShowId?: number) => void;
}

const ShowGeneralInfoSchema = z
  .object({
    name: z.string().min(1, 'Show name is required'),
    startDate: z.date({ required_error: 'Start date is required' }),
    endDate: z.date({ required_error: 'End date is required' }),
    displayDate: z.date({ required_error: 'Display date is required' }),
    orderDeadlineDate: z.date({
      required_error: 'Order deadline date is required',
    }),
    lateChargePercentage: z.string(),
    link: z.string().optional(),
    description: z.string().optional(),
    display: z.boolean(),
    locationId: z.string().min(1, 'Location is required'),
    kioskPrintingQueueDate: z.date().optional(),
  })
  .refine(
    (data) => {
      // Validate that end date is after start date
      if (data.startDate && data.endDate) {
        return (
          isAfter(data.endDate, data.startDate) ||
          data.startDate.getTime() === data.endDate.getTime()
        );
      }
      return true;
    },
    {
      message: 'End date must be after or equal to start date',
      path: ['endDate'],
    },
  )
  .refine(
    (data) => {
      // Validate that display date is between start and end date
      if (data.startDate && data.endDate && data.displayDate) {
        return (
          (isAfter(data.displayDate, data.startDate) ||
            data.startDate.getTime() === data.displayDate.getTime()) &&
          (isBefore(data.displayDate, data.endDate) ||
            data.endDate.getTime() === data.displayDate.getTime())
        );
      }
      return true;
    },
    {
      message: 'Display date must be between start and end date',
      path: ['displayDate'],
    },
  );

function FormContent({
  defaultValues,
  showId,
  onSuccess,
}: {
  defaultValues?: any;
  showId?: number;
  onSuccess?: (newShowId?: number) => void;
}) {
  const { toast } = useToast();
  const isEditMode = !!showId;

  const { data: locations } = useQuery({
    queryKey: ShowLocationQuery.tags,
    queryFn: ShowLocationQuery.getAll,
  });

  const form = useForm<any>({
    resolver: zodResolver(ShowGeneralInfoSchema),
    mode: 'onChange',
    defaultValues: defaultValues ?? {
      name: '',
      code: '',
      startDate: undefined,
      endDate: undefined,
      displayDate: undefined,
      orderDeadlineDate: undefined,
      lateChargePercentage: '0',
      link: '',
      description: '',
      display: true,
      locationId: '',
      kioskPrintingQueueDate: undefined,
      view: true,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: any) => {
      // Helper function to format dates properly using date-fns
      const formatDate = (dateValue: any, time: string) => {
        if (!dateValue) return '';

        try {
          let date: Date;

          // Handle different input types
          if (dateValue instanceof Date) {
            date = dateValue;
          } else if (typeof dateValue === 'string') {
            // Try to parse ISO string first, then fallback to new Date()
            date = dateValue.includes('T')
              ? parseISO(dateValue)
              : new Date(dateValue + 'T23:59:59');
          } else {
            return '';
          }

          // Validate the date
          if (!isValid(date)) {
            return '';
          }

          // Format to YYYY-MM-DD and append time
          return format(date, 'yyyy-MM-dd') + time;
        } catch (error) {
          return '';
        }
      };

      const formattedData: any = {
        name: data.name,
        startDate: formatDate(data.startDate, 'T23:59:59'),
        endDate: formatDate(data.endDate, 'T23:59:59'),
        displayDate: formatDate(data.displayDate, 'T23:59:59'),
        orderDeadlineDate: formatDate(data.orderDeadlineDate, 'T23:59:59'),
        lateChargePercentage: data.lateChargePercentage,
        display: data.display,
        locationId: Number(data.locationId),
      };

      // Only include optional fields if they have values
      if (data.link && data.link.trim()) {
        formattedData.link = data.link;
      }

      if (data.description && data.description.trim()) {
        formattedData.description = data.description;
      }

      const kioskDate = formatDate(data.kioskPrintingQueueDate, 'T23:59:59');
      if (kioskDate) {
        formattedData.kioskPrintingQueueDate = kioskDate;
      }

      if (isEditMode) {
        await ShowQuery.update(showId!)(formattedData);
      } else {
        return ShowQuery.create(formattedData);
      }
    },
    onSuccess: async (newShowId?: number) => {
      await getQueryClient().invalidateQueries({
        queryKey: ShowQuery.tags,
      });

      if (showId) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Shows', showId],
        });
      }

      toast({
        title: 'Success',
        description: isEditMode
          ? 'Show updated successfully'
          : 'Show created successfully',
      });

      onSuccess?.(newShowId);
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save show',
        variant: 'destructive',
      });
    },
  });

  const locationOptions =
    locations?.map((location) => ({
      label: location.name || `Location ${location.id}`,
      value: location.id.toString(),
    })) || [];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              General Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Field
                control={form.control}
                name="name"
                label="Show Name"
                type="text"
                required
              />

              {isEditMode && (
                <Field
                  control={form.control}
                  name="code"
                  label="Show Code"
                  type="text"
                  disabled
                  className="bg-slate-50"
                />
              )}

              <Field
                control={form.control}
                name="locationId"
                label="Location"
                type={{
                  type: 'select',
                  props: {
                    options: locationOptions,
                    placeholder:
                      locationOptions.length > 0
                        ? 'Select Location'
                        : 'No locations available',
                  },
                }}
                required
              />

              <Field
                control={form.control}
                name="startDate"
                label="Start Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="endDate"
                label="End Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="displayDate"
                label="Display Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="orderDeadlineDate"
                label="Order Deadline Date"
                type="date"
                required
              />

              <Field
                control={form.control}
                name="lateChargePercentage"
                label="Late Charge Percentage"
                type="number"
                required
              />

              <Field
                control={form.control}
                name="kioskPrintingQueueDate"
                label="Kiosk Printing Queue Date"
                type="date"
              />

              <div className="md:col-span-2">
                <Field
                  control={form.control}
                  name="link"
                  label="Show Link"
                  type="text"
                />
              </div>

              <div className="md:col-span-2">
                <Field
                  control={form.control}
                  name="description"
                  label="Description"
                  type="textarea"
                />
              </div>

              <Field
                control={form.control}
                name="display"
                label="Display Show"
                type="checkbox"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6 pt-4 border-t border-slate-200">
          <Button
            type="submit"
            className="bg-[#00646C] hover:bg-[#00646C]/90"
            disabled={isPending}
          >
            {isPending ? <Spinner className="mr-2" /> : null}
            {isPending
              ? 'Saving...'
              : isEditMode
                ? 'Update & Continue'
                : 'Save & Continue'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function GeneralInfoTab({
  showId,
  onSuccess,
}: GeneralInfoTabProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: !!showId,
    select: (res: Show) => ({
      name: res.name || '',
      code: res.code || '',
      startDate: res.startDate ? new Date(res.startDate) : undefined,
      endDate: res.endDate ? new Date(res.endDate) : undefined,
      displayDate: res.displayDate ? new Date(res.displayDate) : undefined,
      orderDeadlineDate: res.orderDeadlineDate
        ? new Date(res.orderDeadlineDate)
        : undefined,
      lateChargePercentage: res.lateChargePercentage || '0',
      link: res.link || '',
      description: res.description || '',
      display: res.display,
      locationId: res.locationId ? res.locationId.toString() : '',
      kioskPrintingQueueDate: res.kioskPrintingQueueDate
        ? new Date(res.kioskPrintingQueueDate)
        : undefined,
      view: res.view,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} showId={showId} onSuccess={onSuccess} />
    </Suspense>
  );
}

import React from 'react';
import { GalleryImage } from '@/models/Gallery';
import { But<PERSON> } from '@/components/ui/button';
import { Edit2, Trash2, Download, Eye } from 'lucide-react';

interface GalleryImageGridProps {
  images: GalleryImage[];
  isLoading?: boolean;
  onPreview: (url: string) => void;
  onEdit: (img: GalleryImage) => void;
  onDelete: (id: number) => void;
  onDownload: (id: number, name: string) => void;
}

const GalleryImageGrid: React.FC<GalleryImageGridProps> = ({
  images,
  isLoading,
  onPreview,
  onEdit,
  onDelete,
  onDownload,
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="rounded shadow bg-white p-4 animate-pulse h-64"
          />
        ))}
      </div>
    );
  }
  if (!images.length) {
    return (
      <div className="text-center text-gray-500 py-12">No images found.</div>
    );
  }
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {images.map((img) => (
        <div
          key={img.id}
          className="bg-white rounded-xl shadow-md border border-slate-200 flex flex-col overflow-hidden hover:shadow-2xl hover:border-primary/60 transition-all group"
        >
          <div
            className="relative cursor-pointer aspect-[3/2] bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center overflow-hidden"
            onClick={() => onPreview(img.url)}
            title="Preview"
          >
            <img
              src={
                img.url.startsWith('/images') ? img.url : '/images' + img.url
              }
              alt={img.alt || img.name}
              className="object-cover w-full h-full transition group-hover:scale-105 group-hover:opacity-80 duration-200"
            />
            <div className="absolute inset-0 bg-black/10 opacity-0 group-hover:opacity-100 transition" />
            <Button
              size="icon"
              variant="secondary"
              className="absolute top-2 right-2 opacity-80 hover:opacity-100 z-10"
              onClick={(e) => {
                e.stopPropagation();
                onPreview(img.url);
              }}
              title="Preview"
            >
              <Eye className="w-5 h-5" />
            </Button>
          </div>
          <div className="flex-1 flex flex-col p-4 gap-1">
            <div className="font-semibold text-base truncate" title={img.name}>
              {img.name}
            </div>
            <div
              className="text-xs text-slate-500 truncate"
              title={img.categoryName}
            >
              Category: {img.categoryName}
            </div>
            <div
              className="text-xs text-slate-500 truncate"
              title={img.subcategoryName}
            >
              Subcategory: {img.subcategoryName}
            </div>
            <div className="flex w-full item-center justify-center gap-4 mt-3">
              <Button
                size="icon"
                variant="outline"
                onClick={() => onEdit(img)}
                title="Edit"
                className="hover:border-primary"
              >
                <Edit2 className="w-4 h-4" />
              </Button>
              <Button
                size="icon"
                variant="destructive"
                onClick={() => onDelete(img.id)}
                title="Delete"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
              <Button
                size="icon"
                variant="secondary"
                onClick={() => onDownload(img.id, img.name)}
                title="Download"
              >
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default GalleryImageGrid;

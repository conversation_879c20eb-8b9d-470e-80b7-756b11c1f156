'use client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import GalleryQuery from '@/services/queries/GalleryQuery';
import { GalleryCategory } from '@/models/Gallery';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import CategoryModal from './CategoryModal';
import { Edit2, Trash2 } from 'lucide-react';

function ShowMoreCell({
  text,
  maxLength = 60,
}: {
  text: string;
  maxLength?: number;
}) {
  const [showMore, setShowMore] = useState(false);
  if (!text) return '-';
  if (text.length <= maxLength) return text;
  return (
    <span>
      {showMore ? text : text.slice(0, maxLength) + '...'}{' '}
      <button
        type="button"
        className="text-blue-600 underline text-xs ml-1"
        onClick={() => setShowMore((v) => !v)}
      >
        {showMore ? 'Show less' : 'Show more'}
      </button>
    </span>
  );
}

export default function CategoryManagementSection() {
  const queryClient = useQueryClient();
  const { data, isLoading } = useQuery({
    queryKey: ['gallery', 'categories'],
    queryFn: GalleryQuery.getCategories,
  });

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [editData, setEditData] = useState<GalleryCategory | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  // Mutations
  const addMutation = useMutation({
    mutationFn: (addData: {
      name: string;
      description: string;
      displayOrder: number;
    }) =>
      GalleryQuery.createCategory({
        name: addData.name,
        description: addData.description ?? '',
        displayOrder: addData.displayOrder,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gallery', 'categories'] });
      toast({ title: 'Category created', variant: 'success' });
      setModalOpen(false);
    },
    onError: (e: any) =>
      toast({
        title: e.message || 'Failed to create category',
        variant: 'destructive',
      }),
  });
  const editMutation = useMutation({
    mutationFn: (editDataInfo: {
      id: number;
      name: string;
      description: string;
      displayOrder: number;
    }) =>
      GalleryQuery.updateCategory(editDataInfo.id)({
        name: editDataInfo.name,
        description: editDataInfo.description ?? '',
        displayOrder: editDataInfo.displayOrder,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gallery', 'categories'] });
      toast({ title: 'Category updated', variant: 'success' });
      setModalOpen(false);
      setEditData(null);
    },
    onError: (e: any) =>
      toast({
        title: e.message || 'Failed to update category',
        variant: 'destructive',
      }),
  });
  const deleteMutation = useMutation({
    mutationFn: (id: number) => GalleryQuery.deleteCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gallery', 'categories'] });
      toast({ title: 'Category deleted', variant: 'success' });
      setDeleteId(null);
    },
    onError: (e: any) =>
      toast({
        title: e.message || 'Failed to delete category',
        variant: 'destructive',
      }),
  });

  // Table columns
  const columns = useMemo<ColumnDef<GalleryCategory>[]>(() => {
    return generateTableColumns<GalleryCategory>(
      {
        name: { name: 'Name', type: 'text', sortable: true },
        description: {
          name: 'Description',
          type: {
            type: 'node',
            render: ({ cell }) => <ShowMoreCell text={cell} maxLength={60} />,
          },
        },
        displayOrder: { name: 'Display Order', type: 'text', sortable: true },
      },
      {
        actions: {
          name: 'Actions',
          type: {
            type: 'node',
            render: ({ row }) => (
              <div className="flex gap-2">
                <Button
                  size="icon"
                  variant="outline"
                  onClick={() => {
                    setEditData(row);
                    setModalOpen(true);
                  }}
                >
                  <Edit2 className="size-4" />
                </Button>
                <Button
                  size="icon"
                  variant="destructive"
                  onClick={() => setDeleteId(row.id)}
                >
                  <Trash2 className="size-4" />
                </Button>
              </div>
            ),
          },
        },
      },
      false,
    );
  }, []);

  // Table filters
  const filters = useMemo(
    () =>
      generateTableFilters<GalleryCategory>({
        name: { name: 'Name', type: 'text' },
        description: { name: 'Description', type: 'text' },
      }),
    [],
  );

  return (
    <div>
      <DataTable
        columns={columns}
        data={data || []}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <Button
            variant="main"
            onClick={() => {
              setEditData(null);
              setModalOpen(true);
            }}
          >
            Add Category
          </Button>
        }
      />
      {/* Add/Edit Modal */}
      <CategoryModal
        open={modalOpen}
        onOpenChange={(open) => {
          setModalOpen(open);
          if (!open) setEditData(null);
        }}
        onSubmit={(values) => {
          setLoading(true);
          if (editData) {
            editMutation.mutate({
              id: editData.id,
              name: values.name,
              description: values.description ?? '',
              displayOrder: values.displayOrder,
            });
          } else {
            addMutation.mutate({
              name: values.name,
              description: values.description ?? '',
              displayOrder: values.displayOrder,
            });
          }
          setLoading(false);
        }}
        initialData={
          editData
            ? {
                name: editData.name,
                description: editData.description,
                displayOrder: editData.displayOrder,
              }
            : undefined
        }
        loading={
          addMutation.status === 'pending' || editMutation.status === 'pending'
        }
      />
      {/* Delete Confirmation */}
      <AlertDialog
        open={!!deleteId}
        onOpenChange={(open) => !open && setDeleteId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Category</AlertDialogTitle>
          </AlertDialogHeader>
          <div>Are you sure you want to delete this category?</div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteId(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteId && deleteMutation.mutate(deleteId)}
            >
              {deleteMutation.status === 'pending' ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

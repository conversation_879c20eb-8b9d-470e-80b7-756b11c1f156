import * as z from 'zod';

export const ShowHallSchema = z.object({
  locationId: z.number({
    required_error: 'Location ID is required',
    invalid_type_error: 'Location ID must be a number',
  }),
  hallName: z.string().min(1, { message: 'Hall name is required' }),
  hallCode: z.string().optional().nullable(),
  hallStyle: z.string().optional().nullable(),
  hallFloorType: z.string().optional().nullable(),
  banquetCapacity: z.string().optional().nullable(),
  hallWidth: z.string().optional().nullable(),
  hallLength: z.string().optional().nullable(),
  overheadHeight: z.string().optional().nullable(),
  hallArea: z.string().optional().nullable(),
  isElecOnFloor: z.boolean().default(false),
  isElecOnCeiling: z.boolean().default(false),
  hallSurface: z.string().optional().nullable(),
  hallCeilingHeight: z.string().optional().nullable(),
  accessDoor: z.string().optional().nullable(),
  loadingDocks: z.string().optional().nullable(),
  hallBoothCount: z.string().optional().nullable(),
  isArchived: z.boolean().default(false),
  floorPlanPath: z.string().optional().nullable(),
  floorPlan: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .min(1, 'Please select at least one file.')
    .max(1)
    .nullable(),
});

export type ShowHallData = z.infer<typeof ShowHallSchema>;

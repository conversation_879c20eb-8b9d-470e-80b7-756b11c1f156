'use client';

import { useState, useEffect } from 'react';
import { DashboardHeader } from '@/components/dashboard-header';
import { DashboardSidebar } from '@/components/dashboard-sidebar';
import { DashboardContent } from '@/components/dashboard-content';
import { SidebarProvider } from '@/components/ui/sidebar';
import { FactBox } from '@/components/fact-box';
import { KeyboardShortcutsDialog } from '@/components/keyboard-shortcuts-dialog';
import { useToast } from '@/hooks/use-toast';

export default function Dashboard() {
  const [showFactBox, setShowFactBox] = useState(true);
  const [currentRole, setCurrentRole] = useState('finance');
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const { toast } = useToast();

  // Auto-save notification demo
  useEffect(() => {
    const interval = setInterval(() => {
      toast({
        title: 'Changes saved',
        description: 'All your changes have been automatically saved',
        duration: 2000,
      });
    }, 120000); // Show auto-save notification every 2 minutes

    return () => clearInterval(interval);
  }, [toast]);

  // Keyboard shortcut for help dialog
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Shift + ? for keyboard shortcuts
      if (e.shiftKey && e.key === '?') {
        setShowKeyboardShortcuts(true);
      }

      // Ctrl/Cmd + B for toggling FactBox
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        setShowFactBox((prev) => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen flex-col bg-slate-50">
        <DashboardHeader
          showFactBox={showFactBox}
          setShowFactBox={setShowFactBox}
          currentRole={currentRole}
          setCurrentRole={setCurrentRole}
          setShowKeyboardShortcuts={setShowKeyboardShortcuts}
        />
        <div className="flex flex-1 overflow-hidden">
          <DashboardSidebar currentRole={currentRole} />
          <main className="flex-1 overflow-y-auto bg-slate-50 p-4 md:p-6">
            <DashboardContent currentRole={currentRole} />
          </main>
          {showFactBox && <FactBox currentRole={currentRole} />}
        </div>
        <KeyboardShortcutsDialog
          open={showKeyboardShortcuts}
          onOpenChange={setShowKeyboardShortcuts}
        />
      </div>
    </SidebarProvider>
  );
}

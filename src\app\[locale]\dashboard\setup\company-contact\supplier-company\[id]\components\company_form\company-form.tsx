'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  CompanyCreateSchema,
  CompanyUpdateSchema,
  CompanyCreateData,
  CompanyUpdateData,
} from '@/schema/CompanySchema';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Company } from '@/models/Company';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { useEffect } from 'react';

interface CompanyFormProps {
  id?: number;
}

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: any;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();
  const isEdit = !!id;

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const form = useForm<any>({
    resolver: zodResolver(isEdit ? CompanyUpdateSchema : CompanyCreateSchema),
    defaultValues: defaultValues ?? {
      name: '',
      phone: '',
      email: '',
      address1: '',
      address2: '',
      city: '',
      provinceId: '',
      postalCode: '',
      countryId: '',
      websiteUrl: '',
      accountNumber: '',
      note: '',
      isArchived: false,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CompanyCreateData | CompanyUpdateData) => {
      const dataWithGroup = { ...data, companyGroup: 'Supplier' };

      if (isEdit) {
        await CompanyQuery.update(id!)(dataWithGroup as CompanyUpdateData);
      } else {
        await CompanyQuery.create(dataWithGroup as CompanyCreateData);
      }
    },
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [...CompanyQuery.tags],
      });
      toast({
        title: 'Success',
        description: isEdit
          ? 'Company updated successfully.'
          : 'Company created successfully.',
        variant: 'success',
      });
      push('/dashboard/setup/company-contact/supplier-company');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Something went wrong.',
        variant: 'destructive',
      });
    },
  });

  useEffect(() => {
    const selectedProvinceId = form.getValues('provinceId');

    if (selectedProvinceId && provinces) {
      const matchedProvince = provinces.find(
        (p) => p.id.toString() === selectedProvinceId,
      );

      if (matchedProvince?.countryId) {
        const matchedCountryId = matchedProvince.countryId.toString();
        if (form.getValues('countryId') !== matchedCountryId) {
          form.setValue('countryId', matchedCountryId);
        }
      }
    }
  }, [form.watch('provinceId')]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-2">
          <Field
            control={form.control}
            name="name"
            label="Company Name"
            placeholder="Enter company name"
            type="text"
            required
          />
          <Field
            control={form.control}
            name="email"
            label="Email"
            placeholder="Enter email address"
            type="email"
          />
          <Field
            control={form.control}
            name="phone"
            label="Phone"
            type="phone"
          />
          <Field
            control={form.control}
            name="websiteUrl"
            label="Website URL"
            placeholder="Enter website URL"
            type="text"
          />
          <Field
            control={form.control}
            name="address1"
            label="Address Line 1"
            placeholder="Enter street address"
            type="text"
          />
          <Field
            control={form.control}
            name="address2"
            label="Address Line 2"
            placeholder="Enter address line 2"
            type="text"
          />
          <Field
            control={form.control}
            name="city"
            label="City"
            placeholder="Enter city"
            type="text"
          />
          <Field
            control={form.control}
            name="postalCode"
            label="Postal Code"
            placeholder="Enter postal code"
            type="text"
          />
          {isLoadingProvinces ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="provinceId"
              label="Province"
              type={{
                type: 'select',
                props: {
                  options:
                    provinces
                      ?.sort((a, b) => a.countryId - b.countryId)
                      .map((p) => ({
                        label: p.name,
                        value: p.id.toString(),
                      })) ?? [],
                  placeholder: 'Select Province',
                },
              }}
            />
          )}
          {isLoadingCountries ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="countryId"
              label="Country"
              type={{
                type: 'select',
                props: {
                  options:
                    (form.watch('provinceId') == undefined
                      ? (countries ?? [])
                      : countries &&
                        countries.filter(
                          (c) => c.id.toString() === form.watch('countryId'),
                        )
                    )?.map((b) => ({
                      value: b.id.toString(),
                      label: b.name,
                    })) ?? [],
                  placeholder: 'Select a country',
                },
              }}
            />
          )}
          <Field
            control={form.control}
            name="accountNumber"
            label="Account Number"
            placeholder="Enter account number"
            type="text"
          />
          <Field
            control={form.control}
            name="note"
            label="Note"
            placeholder="Enter note"
            type="textarea"
            className="md:col-span-2"
          />
          {isEdit && (
            <Field
              control={form.control}
              name="isArchived"
              label="Is Archived"
              type="checkbox"
            />
          )}
        </div>

        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              push('/dashboard/setup/company-contact/supplier-company')
            }
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending ? <Spinner className="mr-2" /> : null}
            {isEdit ? 'Update' : 'Create'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function CompanyForm({ id }: CompanyFormProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...CompanyQuery.tags, { id }],
    queryFn: () => CompanyQuery.getOne(id!),
    enabled: !!id,
    select: (res: Company) => ({
      name: res.name,
      phone: res.phone || '',
      email: res.email || '',
      address1: res.address1 || '',
      address2: res.address2 || '',
      city: res.city || '',
      provinceId: res.provinceId?.toString() || '',
      postalCode: res.postalCode || '',
      countryId: res.countryId?.toString() || '',
      websiteUrl: res.websiteUrl || '',
      accountNumber: res.accountNumber || '',
      note: res.note || '',
      isArchived: res.isArchived,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} />
    </Suspense>
  );
}

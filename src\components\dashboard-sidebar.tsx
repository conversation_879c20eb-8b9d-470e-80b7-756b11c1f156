'use client';

import {
  BarChart3,
  Box,
  CreditCard,
  DollarSign,
  FileText,
  Home,
  Package,
  Settings,
  ShoppingCart,
  Truck,
  Users,
  ChevronDown,
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from './ui/Collapsible';

interface DashboardSidebarProps {
  currentRole: string;
}

export function DashboardSidebar({ currentRole }: DashboardSidebarProps) {
  const navigationItems = {
    finance: [
      {
        label: 'Finance',
        icon: DollarSign,
        items: [
          { name: 'General Ledger', url: '#', shortcut: 'F G' },
          { name: 'Accounts Receivable', url: '#', shortcut: 'F R' },
          { name: 'Accounts Payable', url: '#', shortcut: 'F P' },
          { name: 'Cash Management', url: '#', shortcut: 'F C' },
          { name: 'Fixed Assets', url: '#', shortcut: 'F A' },
        ],
      },
      {
        label: 'Reporting',
        icon: FileText,
        items: [
          { name: 'Financial Statements', url: '#', shortcut: 'R F' },
          { name: 'Budget Reports', url: '#', shortcut: 'R B' },
          { name: 'Tax Reports', url: '#', shortcut: 'R T' },
          { name: 'Audit Logs', url: '#', shortcut: 'R A' },
        ],
      },
    ],
    sales: [
      {
        label: 'Sales',
        icon: ShoppingCart,
        items: [
          { name: 'Sales Orders', url: '#', shortcut: 'S O' },
          { name: 'Customers', url: '#', shortcut: 'S C' },
          { name: 'Quotes', url: '#', shortcut: 'S Q' },
          { name: 'Invoices', url: '#', shortcut: 'S I' },
          { name: 'Returns', url: '#', shortcut: 'S R' },
        ],
      },
      {
        label: 'Marketing',
        icon: BarChart3,
        items: [
          { name: 'Campaigns', url: '#', shortcut: 'M C' },
          { name: 'Leads', url: '#', shortcut: 'M L' },
          { name: 'Analytics', url: '#', shortcut: 'M A' },
        ],
      },
    ],
    inventory: [
      {
        label: 'Inventory',
        icon: Box,
        items: [
          { name: 'Items', url: '#', shortcut: 'I I' },
          { name: 'Warehouses', url: '#', shortcut: 'I W' },
          { name: 'Stock Transfers', url: '#', shortcut: 'I T' },
          { name: 'Physical Inventory', url: '#', shortcut: 'I P' },
        ],
      },
      {
        label: 'Purchasing',
        icon: Truck,
        items: [
          { name: 'Purchase Orders', url: '#', shortcut: 'P O' },
          { name: 'Vendors', url: '#', shortcut: 'P V' },
          { name: 'Receipts', url: '#', shortcut: 'P R' },
          { name: 'Returns', url: '#', shortcut: 'P T' },
        ],
      },
    ],
    admin: [
      {
        label: 'Administration',
        icon: Settings,
        items: [
          { name: 'User Management', url: '#', shortcut: 'A U' },
          { name: 'Roles & Permissions', url: '#', shortcut: 'A R' },
          { name: 'Company Setup', url: '#', shortcut: 'A C' },
          { name: 'System Settings', url: '#', shortcut: 'A S' },
        ],
      },
      {
        label: 'Integration',
        icon: Package,
        items: [
          { name: 'APIs', url: '#', shortcut: 'I A' },
          { name: 'Data Import/Export', url: '#', shortcut: 'I D' },
          { name: 'Extensions', url: '#', shortcut: 'I E' },
        ],
      },
    ],
  };

  const commonItems = [
    {
      label: 'Dashboard',
      icon: Home,
      url: '#',
      isActive: true,
      shortcut: 'G D',
    },
    {
      label: 'Users',
      icon: Users,
      url: '#',
      shortcut: 'G U',
    },
    {
      label: 'Payments',
      icon: CreditCard,
      url: '#',
      shortcut: 'G P',
    },
  ];

  const roleItems =
    navigationItems[currentRole as keyof typeof navigationItems] || [];

  return (
    <TooltipProvider>
      <Sidebar className="border-r border-slate-200 bg-white">
        <SidebarHeader className="border-b border-slate-200">
          <div className="flex h-14 items-center px-4">
            <div className="flex items-center gap-2 font-semibold text-slate-800">
              <span className="text-xl">Good Key</span>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                {commonItems.map((item) => (
                  <SidebarMenuItem key={item.label}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton
                          asChild
                          isActive={item.isActive}
                          tooltip={item.label}
                        >
                          <a href={item.url} className="text-slate-700">
                            <item.icon />
                            <span>{item.label}</span>
                          </a>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      <TooltipContent side="right" align="center">
                        <div className="flex flex-col">
                          <span>{item.label}</span>
                          <span className="text-xs text-slate-500">
                            Shortcut: {item.shortcut}
                          </span>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          {roleItems.map((section) => (
            <SidebarGroup key={section.label}>
              <Collapsible defaultOpen className="group/collapsible">
                <SidebarGroupLabel asChild>
                  <CollapsibleTrigger className="flex w-full items-center text-slate-700">
                    {section.label}
                    <ChevronDown className="ml-auto h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
                  </CollapsibleTrigger>
                </SidebarGroupLabel>
                <CollapsibleContent>
                  <SidebarGroupContent>
                    <SidebarMenu>
                      {section.items.map((item) => (
                        <SidebarMenuItem key={item.name}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <SidebarMenuButton asChild tooltip={item.name}>
                                <a
                                  href={item.url}
                                  className="text-slate-600 hover:text-slate-900"
                                >
                                  <span>{item.name}</span>
                                </a>
                              </SidebarMenuButton>
                            </TooltipTrigger>
                            <TooltipContent side="right" align="center">
                              <div className="flex flex-col">
                                <span>{item.name}</span>
                                <span className="text-xs text-slate-500">
                                  Shortcut: {item.shortcut}
                                </span>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </CollapsibleContent>
              </Collapsible>
            </SidebarGroup>
          ))}
        </SidebarContent>
        <SidebarRail />
      </Sidebar>
    </TooltipProvider>
  );
}

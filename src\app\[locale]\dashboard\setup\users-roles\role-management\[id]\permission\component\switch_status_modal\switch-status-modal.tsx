import { useMutation } from '@tanstack/react-query';

import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { cn } from '@/lib/utils';
import UsersQuery from '@/services/queries/UsersQuery';
import { Button } from '@/components/ui/button';
interface ISwitchStatusModal {
  close: () => void;
  isChecked: boolean;
  permissionId: number;
}
export default function SwitchStatusModal({
  close,
  isChecked,
  permissionId,
}: ISwitchStatusModal) {
  const { mutate, isPending } = useMutation({
    mutationKey: UsersQuery.tags,
    mutationFn: UsersQuery.switchStatus,
    onSuccess: async () => {
      // await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });

      close();
    },
  });
  return (
    <ModalContainer
      onSubmit={(e) => {
        e.preventDefault();
        mutate(permissionId);
      }}
      className="gap-0"
      title={isChecked ? 'Activate permission' : 'Deactivate permission'}
      description={
        isChecked
          ? 'Activate the permissions for this role within the system.'
          : 'Deactivate the permissions for this role within the system.'
      }
      controls={
        <div className="flex flex-row gap-2">
          <Button
            disabled={isPending}
            type="submit"
            className={cn({
              'bg-green-500': isChecked,
              'bg-destructive': !isChecked,
            })}
          >
            {isPending
              ? 'Please wait...'
              : isChecked
                ? 'Activate'
                : 'Deactivate'}
          </Button>
          <Button onClick={close} variant="secondary" disabled={isPending}>
            Cancel
          </Button>
        </div>
      }
    />
  );
}

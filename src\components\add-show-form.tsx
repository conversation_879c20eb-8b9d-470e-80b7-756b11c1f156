'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react';

import { useForm, FormProvider } from 'react-hook-form';
import Field from '@/components/ui/inputs/field';

const steps = [
  'Setup New Show',
  'Hall Details',
  'Promoter Info',
  'Show Schedule',
  'Dates & Times',
  'Options & Settings',
  'Booth Options',
  'Products',
  'Services',
  'Documents',
  'Review & Submit',
];

// Define types for the different day schedules
interface DaySchedule {
  id: string;
  date: Date | undefined;
  startTime: string;
  endTime: string;
  notes: string;
}

// Define the form data type for useForm
interface AddShowFormValues {
  showName: string;
  showCode: string;
  startDate?: Date;
  endDate?: Date;
  displayDate: string;
  orderDeadlineDate?: Date;
  lateChargePercentage: string;
  kioskPrintingQueueDate?: Date;
  showSite: string;
  showIsIn: string;
  showLink: string;
  showSiteDirection: string;
  showDescription: string;
  makeAvailable: boolean;
  showInventory: string;
  labourNonTaxable: boolean;
  hall: string;
  showSiteContact: string;
  aisleCarpeting: boolean;
  carpetColour: string;
  additionalRequirements: string;
  showManager: string;
  billedTo: string;
  showSubcontract: boolean;
  floorPlanRequired: boolean;
  taxOption: string;
  advanceMaterialWarehouse: string;
  scheduleComment: string;
  moveInDays: DaySchedule[];
  showDays: DaySchedule[];
  moveOutDays: DaySchedule[];
}

export default function AddShowForm() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const form = useForm<AddShowFormValues>({
    defaultValues: {
      showName: '',
      showCode: '',
      startDate: undefined,
      endDate: undefined,
      displayDate: '',
      orderDeadlineDate: undefined,
      lateChargePercentage: '35',
      kioskPrintingQueueDate: undefined,
      showSite: '',
      showIsIn: '',
      showLink: 'http://www.example.com',
      showSiteDirection: '',
      showDescription: '',
      makeAvailable: true,
      showInventory: '',
      labourNonTaxable: false,
      hall: '',
      showSiteContact: '',
      aisleCarpeting: false,
      carpetColour: '',
      additionalRequirements: '',
      showManager: '',
      billedTo: '',
      showSubcontract: false,
      floorPlanRequired: true,
      taxOption: 'gst',
      advanceMaterialWarehouse: '',
      scheduleComment: '',
      moveInDays: [
        {
          id: 'move-in-1',
          date: undefined,
          startTime: '08:00',
          endTime: '17:00',
          notes: '',
        },
      ],
      showDays: [
        {
          id: 'show-day-1',
          date: undefined,
          startTime: '09:00',
          endTime: '18:00',
          notes: '',
        },
      ],
      moveOutDays: [
        {
          id: 'move-out-1',
          date: undefined,
          startTime: '08:00',
          endTime: '17:00',
          notes: '',
        },
      ],
    },
  });

  // Add this after the formData state
  const [scheduleItems, setScheduleItems] = useState<
    Array<{
      id: string;
      date: Date;
      type: string;
      startTime: string;
      endTime: string;
    }>
  >([]);

  // Add state for move-in, show, and move-out days
  const [moveInDays, setMoveInDays] = useState<DaySchedule[]>([
    {
      id: 'move-in-1',
      date: undefined,
      startTime: '08:00',
      endTime: '17:00',
      notes: '',
    },
  ]);

  const [showDays, setShowDays] = useState<DaySchedule[]>([
    {
      id: 'show-day-1',
      date: undefined,
      startTime: '09:00',
      endTime: '18:00',
      notes: '',
    },
  ]);

  const [moveOutDays, setMoveOutDays] = useState<DaySchedule[]>([
    {
      id: 'move-out-1',
      date: undefined,
      startTime: '08:00',
      endTime: '17:00',
      notes: '',
    },
  ]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    form.setValue(name as keyof AddShowFormValues, value as any);
  };

  const handleCheckboxChange = (
    name: keyof AddShowFormValues,
    checked: boolean,
  ) => {
    form.setValue(name, checked as any);
  };

  const handleSelectChange = (name: keyof AddShowFormValues, value: string) => {
    form.setValue(name, value as any);
  };

  const handleDateChange = (
    name: keyof AddShowFormValues,
    date: Date | undefined,
  ) => {
    form.setValue(name, date as any);
  };

  const handleRadioChange = (name: keyof AddShowFormValues, value: string) => {
    form.setValue(name, value as any);
  };

  // Add this after the handleRadioChange function
  const handleAddScheduleItem = () => {
    // In a real app, you would open a modal or form to collect schedule item details
    // For this demo, we'll add a sample item
    const newItem = {
      id: `item-${scheduleItems.length + 1}`,
      date: new Date(),
      type:
        scheduleItems.length % 2 === 0
          ? 'Exhibitor Move-in'
          : 'Exhibitor Move-out',
      startTime: '08:00',
      endTime: '17:00',
    };
    setScheduleItems([...scheduleItems, newItem]);
  };

  const handleRemoveScheduleItem = (id: string) => {
    setScheduleItems(scheduleItems.filter((item) => item.id !== id));
  };

  // Add handlers for move-in, show, and move-out days
  const handleDayDateChange = (
    dayType: 'moveIn' | 'show' | 'moveOut',
    id: string,
    date: Date | undefined,
  ) => {
    if (dayType === 'moveIn') {
      setMoveInDays(
        moveInDays.map((day) => (day.id === id ? { ...day, date } : day)),
      );
    } else if (dayType === 'show') {
      setShowDays(
        showDays.map((day) => (day.id === id ? { ...day, date } : day)),
      );
    } else {
      setMoveOutDays(
        moveOutDays.map((day) => (day.id === id ? { ...day, date } : day)),
      );
    }
  };

  const handleDayTimeChange = (
    dayType: 'moveIn' | 'show' | 'moveOut',
    id: string,
    field: 'startTime' | 'endTime',
    value: string,
  ) => {
    if (dayType === 'moveIn') {
      setMoveInDays(
        moveInDays.map((day) =>
          day.id === id ? { ...day, [field]: value } : day,
        ),
      );
    } else if (dayType === 'show') {
      setShowDays(
        showDays.map((day) =>
          day.id === id ? { ...day, [field]: value } : day,
        ),
      );
    } else {
      setMoveOutDays(
        moveOutDays.map((day) =>
          day.id === id ? { ...day, [field]: value } : day,
        ),
      );
    }
  };

  const handleDayNotesChange = (
    dayType: 'moveIn' | 'show' | 'moveOut',
    id: string,
    value: string,
  ) => {
    if (dayType === 'moveIn') {
      setMoveInDays(
        moveInDays.map((day) =>
          day.id === id ? { ...day, notes: value } : day,
        ),
      );
    } else if (dayType === 'show') {
      setShowDays(
        showDays.map((day) => (day.id === id ? { ...day, notes: value } : day)),
      );
    } else {
      setMoveOutDays(
        moveOutDays.map((day) =>
          day.id === id ? { ...day, notes: value } : day,
        ),
      );
    }
  };

  const handleAddDay = (dayType: 'moveIn' | 'show' | 'moveOut') => {
    if (dayType === 'moveIn') {
      const newDay = {
        id: `move-in-${moveInDays.length + 1}`,
        date: undefined,
        startTime: '08:00',
        endTime: '17:00',
        notes: '',
      };
      setMoveInDays([...moveInDays, newDay]);
    } else if (dayType === 'show') {
      const newDay = {
        id: `show-day-${showDays.length + 1}`,
        date: undefined,
        startTime: '09:00',
        endTime: '18:00',
        notes: '',
      };
      setShowDays([...showDays, newDay]);
    } else {
      const newDay = {
        id: `move-out-${moveOutDays.length + 1}`,
        date: undefined,
        startTime: '08:00',
        endTime: '17:00',
        notes: '',
      };
      setMoveOutDays([...moveOutDays, newDay]);
    }
  };

  const handleRemoveDay = (
    dayType: 'moveIn' | 'show' | 'moveOut',
    id: string,
  ) => {
    if (dayType === 'moveIn') {
      if (moveInDays.length > 1) {
        setMoveInDays(moveInDays.filter((day) => day.id !== id));
      }
    } else if (dayType === 'show') {
      if (showDays.length > 1) {
        setShowDays(showDays.filter((day) => day.id !== id));
      }
    } else {
      if (moveOutDays.length > 1) {
        setMoveOutDays(moveOutDays.filter((day) => day.id !== id));
      }
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/setup/list-of-shows');
  };

  const handleSaveAndContinue = () => {
    // In a real app, you would save the form data here
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Submit the form and redirect
      router.push('/dashboard/setup/list-of-shows');
    }
  };

  return (
    <div>
      <div className="mb-4">
        <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
          SETUP NEW SHOW
        </h1>
        <p className="text-slate-600 text-sm">
          Please complete the show's information. Required information is marked
          with a <span className="text-red-500">*</span>.
          <br />
          Form Buttons are located at the Bottom of the Form.
        </p>
      </div>

      <div className="flex">
        {/* Vertical tabs */}
        <div className="w-48 shrink-0 border-r border-slate-200 pr-4 mr-6">
          <div className="space-y-1">
            {steps.map((step, index) => (
              <button
                key={index}
                className={`w-full text-left px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  index === currentStep
                    ? 'bg-[#00646C]/10 text-[#00646C] border-l-2 border-[#00646C]'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
                onClick={() => setCurrentStep(index)}
              >
                {step}
              </button>
            ))}
          </div>
        </div>

        {/* Form content */}
        <div className="flex-1">
          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(handleSaveAndContinue)}>
              {currentStep === 0 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      General Information
                    </h2>

                    <div className="space-y-3">
                      <Field
                        control={form.control}
                        name="showName"
                        label="Show Name"
                        type="text"
                        required
                      />

                      <Field
                        control={form.control}
                        name="showCode"
                        label="Show Code"
                        type="text"
                        required
                      />

                      <Field
                        control={form.control}
                        name="displayDate"
                        label="Display Date"
                        type="text"
                        placeholder="e.g., April 15-17, 2025"
                      />

                      <Field
                        control={form.control}
                        name="showSite"
                        label="Show Site"
                        type={{
                          type: 'select',
                          props: {
                            options: [
                              {
                                label: 'Edmonton Expo Centre',
                                value: 'edmonton_expo',
                              },
                              {
                                label: 'Calgary BMO Centre',
                                value: 'calgary_bmo',
                              },
                              {
                                label: 'Vancouver Convention Centre',
                                value: 'vancouver_convention',
                              },
                              {
                                label: 'Toronto Metro Convention Centre',
                                value: 'toronto_metro',
                              },
                            ],
                            placeholder: 'Select Show Site',
                          },
                        }}
                        required
                      />

                      <Field
                        control={form.control}
                        name="showIsIn"
                        label="Show is in"
                        type={{
                          type: 'select',
                          props: {
                            options: [
                              { label: 'ALBERTA', value: 'alberta' },
                              {
                                label: 'BRITISH COLUMBIA',
                                value: 'british_columbia',
                              },
                              { label: 'MANITOBA', value: 'manitoba' },
                              {
                                label: 'NEW BRUNSWICK',
                                value: 'new_brunswick',
                              },
                              { label: 'NEWFOUNDLAND', value: 'newfoundland' },
                              { label: 'NOVA SCOTIA', value: 'nova_scotia' },
                              { label: 'ONTARIO', value: 'ontario' },
                              { label: 'PRINCE EDWARD ISLAND', value: 'pei' },
                              { label: 'QUEBEC', value: 'quebec' },
                              { label: 'SASKATCHEWAN', value: 'saskatchewan' },
                            ],
                            placeholder: 'Select Province',
                          },
                        }}
                        required
                      />

                      <Field
                        control={form.control}
                        name="showLink"
                        label="Show Link"
                        type="text"
                      />

                      <Field
                        control={form.control}
                        name="makeAvailable"
                        label="Make Available"
                        type="checkbox"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 1 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      Hall Details
                    </h2>

                    <div className="space-y-3">
                      <Field
                        control={form.control}
                        name="hall"
                        label="Hall"
                        type="text"
                        required
                      />

                      <Field
                        control={form.control}
                        name="showSiteContact"
                        label="Show Site Contact"
                        type="text"
                      />

                      <Field
                        control={form.control}
                        name="aisleCarpeting"
                        label="Aisle Carpeting"
                        type="checkbox"
                      />

                      <Field
                        control={form.control}
                        name="carpetColour"
                        label="Carpet Colour"
                        type="text"
                      />

                      <Field
                        control={form.control}
                        name="additionalRequirements"
                        label="Additional Requirements"
                        type="textarea"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      Promoter Information
                    </h2>

                    <div className="space-y-3">
                      <Field
                        control={form.control}
                        name="showManager"
                        label="Show Manager"
                        type="text"
                        required
                      />

                      <Field
                        control={form.control}
                        name="billedTo"
                        label="Billed To"
                        type="text"
                        required
                      />

                      <Field
                        control={form.control}
                        name="showSubcontract"
                        label="Show Subcontract"
                        type="checkbox"
                      />

                      <Field
                        control={form.control}
                        name="floorPlanRequired"
                        label="Floor Plan Required"
                        type="checkbox"
                      />

                      <Field
                        control={form.control}
                        name="taxOption"
                        label="Tax Option"
                        type={{
                          type: 'RadioGroup',
                          props: {
                            options: [
                              { label: 'GST', value: 'gst' },
                              { label: 'HST', value: 'hst' },
                              { label: 'PST', value: 'pst' },
                            ],
                          },
                        }}
                      />

                      <Field
                        control={form.control}
                        name="advanceMaterialWarehouse"
                        label="Advance Material Warehouse"
                        type="text"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      Show Schedule
                    </h2>

                    <div className="space-y-6">
                      <div>
                        <h3 className="font-medium text-slate-800 mb-3">
                          Move-In Days
                        </h3>
                        {moveInDays.map((day, index) => (
                          <div
                            key={day.id}
                            className="mb-4 p-4 border border-slate-200 rounded-md"
                          >
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="font-medium">
                                Move-In Day {index + 1}
                              </h4>
                              {moveInDays.length > 1 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleRemoveDay('moveIn', day.id)
                                  }
                                  className="text-red-500 hover:text-red-700"
                                >
                                  Remove
                                </Button>
                              )}
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Field
                                  control={form.control}
                                  name={`moveInDays.${index}.date`}
                                  label={`Move-In Day ${index + 1} Date`}
                                  type="date"
                                  required
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <div className="space-y-2">
                                  <Field
                                    control={form.control}
                                    name={`moveInDays.${index}.startTime`}
                                    label={`Move-In Day ${index + 1} Start Time`}
                                    type="time"
                                    required
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Field
                                    control={form.control}
                                    name={`moveInDays.${index}.endTime`}
                                    label={`Move-In Day ${index + 1} End Time`}
                                    type="time"
                                    required
                                  />
                                </div>
                              </div>
                              <div className="md:col-span-2 space-y-2">
                                <Field
                                  control={form.control}
                                  name={`moveInDays.${index}.notes`}
                                  label={`Move-In Day ${index + 1} Notes`}
                                  type="textarea"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => handleAddDay('moveIn')}
                          className="mt-2"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Move-In Day
                        </Button>
                      </div>

                      <div>
                        <h3 className="font-medium text-slate-800 mb-3">
                          Show Days
                        </h3>
                        {showDays.map((day, index) => (
                          <div
                            key={day.id}
                            className="mb-4 p-4 border border-slate-200 rounded-md"
                          >
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="font-medium">
                                Show Day {index + 1}
                              </h4>
                              {showDays.length > 1 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleRemoveDay('show', day.id)
                                  }
                                  className="text-red-500 hover:text-red-700"
                                >
                                  Remove
                                </Button>
                              )}
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Field
                                  control={form.control}
                                  name={`showDays.${index}.date`}
                                  label={`Show Day ${index + 1} Date`}
                                  type="date"
                                  required
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <div className="space-y-2">
                                  <Field
                                    control={form.control}
                                    name={`showDays.${index}.startTime`}
                                    label={`Show Day ${index + 1} Start Time`}
                                    type="time"
                                    required
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Field
                                    control={form.control}
                                    name={`showDays.${index}.endTime`}
                                    label={`Show Day ${index + 1} End Time`}
                                    type="time"
                                    required
                                  />
                                </div>
                              </div>
                              <div className="md:col-span-2 space-y-2">
                                <Field
                                  control={form.control}
                                  name={`showDays.${index}.notes`}
                                  label={`Show Day ${index + 1} Notes`}
                                  type="textarea"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => handleAddDay('show')}
                          className="mt-2"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Show Day
                        </Button>
                      </div>

                      <div>
                        <h3 className="font-medium text-slate-800 mb-3">
                          Move-Out Days
                        </h3>
                        {moveOutDays.map((day, index) => (
                          <div
                            key={day.id}
                            className="mb-4 p-4 border border-slate-200 rounded-md"
                          >
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="font-medium">
                                Move-Out Day {index + 1}
                              </h4>
                              {moveOutDays.length > 1 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleRemoveDay('moveOut', day.id)
                                  }
                                  className="text-red-500 hover:text-red-700"
                                >
                                  Remove
                                </Button>
                              )}
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Field
                                  control={form.control}
                                  name={`moveOutDays.${index}.date`}
                                  label={`Move-Out Day ${index + 1} Date`}
                                  type="date"
                                  required
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <div className="space-y-2">
                                  <Field
                                    control={form.control}
                                    name={`moveOutDays.${index}.startTime`}
                                    label={`Move-Out Day ${index + 1} Start Time`}
                                    type="time"
                                    required
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Field
                                    control={form.control}
                                    name={`moveOutDays.${index}.endTime`}
                                    label={`Move-Out Day ${index + 1} End Time`}
                                    type="time"
                                    required
                                  />
                                </div>
                              </div>
                              <div className="md:col-span-2 space-y-2">
                                <Field
                                  control={form.control}
                                  name={`moveOutDays.${index}.notes`}
                                  label={`Move-Out Day ${index + 1} Notes`}
                                  type="textarea"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => handleAddDay('moveOut')}
                          className="mt-2"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Move-Out Day
                        </Button>
                      </div>

                      <div className="space-y-2">
                        <Field
                          control={form.control}
                          name="scheduleComment"
                          label="Additional Schedule Comments"
                          type="textarea"
                          placeholder="Enter any additional comments about the schedule..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      Dates & Times
                    </h2>

                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Field
                            control={form.control}
                            name="startDate"
                            label="Show Start Date"
                            type="date"
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Field
                            control={form.control}
                            name="endDate"
                            label="Show End Date"
                            type="date"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Field
                            control={form.control}
                            name="orderDeadlineDate"
                            label="Order Deadline Date"
                            type="date"
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Field
                            control={form.control}
                            name="kioskPrintingQueueDate"
                            label="Kiosk Printing Queue Date"
                            type="date"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Field
                          control={form.control}
                          name="lateChargePercentage"
                          label="Late Charge Percentage"
                          type="number"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 5 && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
                      Options & Settings
                    </h2>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Field
                          control={form.control}
                          name="showInventory"
                          label="Show Inventory"
                          type={{
                            type: 'select',
                            props: {
                              options: [
                                {
                                  label: 'Standard Inventory',
                                  value: 'standard',
                                },
                                {
                                  label: 'Premium Inventory',
                                  value: 'premium',
                                },
                                { label: 'Basic Inventory', value: 'basic' },
                              ],
                              placeholder: 'Select Inventory',
                            },
                          }}
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Field
                          control={form.control}
                          name="labourNonTaxable"
                          label="Labour is Non-Taxable"
                          type="checkbox"
                        />
                      </div>

                      <div className="space-y-2">
                        <Field
                          control={form.control}
                          name="showDescription"
                          label="Show Description"
                          type="textarea"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* For steps 6-10, we'll show placeholder content */}
              {currentStep > 5 && (
                <div className="min-h-[400px] flex items-center justify-center">
                  <div className="text-center">
                    <h2 className="text-xl font-semibold text-slate-800 mb-2">
                      {steps[currentStep]}
                    </h2>
                    <p className="text-slate-500">
                      This step is under development.
                    </p>
                  </div>
                </div>
              )}

              <div className="flex justify-between mt-8 pt-6 border-t border-slate-200">
                <Button
                  variant="outline"
                  className="border-slate-200"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>

                <div className="flex gap-2">
                  {currentStep > 0 && (
                    <Button
                      variant="outline"
                      className="border-slate-200"
                      onClick={() => setCurrentStep(currentStep - 1)}
                    >
                      <ChevronLeft className="mr-2 h-4 w-4" />
                      Previous
                    </Button>
                  )}
                  <Button
                    className="bg-[#00646C] hover:bg-[#00646C]/90"
                    type="submit"
                  >
                    {currentStep < steps.length - 1 ? (
                      <>
                        Save & Continue
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </>
                    ) : (
                      'Submit'
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </div>
  );
}

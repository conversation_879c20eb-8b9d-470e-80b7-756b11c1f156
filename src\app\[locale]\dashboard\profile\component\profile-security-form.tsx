import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { PasswordSchema } from '@/schema/common';
import VerificationSchema from '@/schema/Verification';
import AuthQuery from '@/services/queries/AuthQuery';
import { useToast } from '@/components/ui/use-toast';

const schema = z
  .object({
    currentPassword: PasswordSchema,
  })
  .and(VerificationSchema);

export default function ProfileSecurityForm() {
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
  });
  const { toast } = useToast();
  const { mutate, isPending } = useMutation({
    mutationKey: [AuthQuery.tags.password],
    mutationFn: AuthQuery.changePassword,
    onSuccess: async () => {
      toast({ title: 'Password changed successfully', variant: 'default' });
      form.reset();
    },
  });
  const { control, handleSubmit } = form;
  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-6 w-full"
        onSubmit={handleSubmit((data) => {
          mutate({
            oldPassword: data.currentPassword,
            password: data.password,
          });
        })}
      >
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Field
              control={control}
              name="password"
              label="New Password"
              type="password"
            />
          </div>
          <div className="space-y-2">
            <Field
              control={control}
              name="confirmPassword"
              label="Confirm Password"
              type="password"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Field
            control={control}
            name="currentPassword"
            label="Current Password"
            type="password"
          />
        </div>
        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

'use client';
import { useEffect, useState } from 'react';
import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import OfferingQuery from '@/services/queries/OfferingQuery';
import CategoryQuery from '@/services/queries/CategoryQuery';
import { Spinner } from '@/components/ui/spinner';
import { CategoryWithOfferingsDto } from '@/models/Offering';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { getQueryClient } from '@/utils/query-client';

interface IAddOnSection {
  id: number; // offering id
}

function FormContent({
  defaultValues,
  id,
  allAddOn,
}: {
  defaultValues: any;
  id: number;
  allAddOn?: CategoryWithOfferingsDto[];
}) {
  const [selectedOfferings, setSelectedOfferings] = useState<
    Record<number, boolean>
  >({});
  const [selectedOptions, setSelectedOptions] = useState<
    Record<number, boolean>
  >({});

  const { push } = useRouter();
  const { toast } = useToast();

  // Sync the selected offerings and options with the default values from the API
  useEffect(() => {
    if (defaultValues) {
      // Initialize selected offerings and options
      const offeringMap: Record<number, boolean> = {};
      const optionMap: Record<number, boolean> = {};

      // Set selected offerings and options based on default values
      defaultValues.selectedOfferings.forEach((offeringId: number) => {
        offeringMap[offeringId] = true;
      });

      defaultValues.selectedOptions.forEach((optionId: number) => {
        optionMap[optionId] = true;
      });

      setSelectedOfferings(offeringMap);
      setSelectedOptions(optionMap);
    }
  }, [defaultValues]);

  const handleOfferingChange = (offeringId: number, optionIds: number[]) => {
    setSelectedOfferings((prev) => {
      const isChecked = !prev[offeringId];
      const updated = { ...prev, [offeringId]: isChecked };

      // Update all related options
      setSelectedOptions((prevOptions) => {
        const updatedOptions = { ...prevOptions };
        optionIds.forEach((i) => {
          updatedOptions[i] = isChecked;
        });
        return updatedOptions;
      });

      return updated;
    });
  };

  const handleOptionChange = (
    offeringId: number,
    optionId: number,
    allOptionIds: number[],
  ) => {
    setSelectedOptions((prevOptions) => {
      const updated = { ...prevOptions, [optionId]: !prevOptions[optionId] };

      const allSelected = allOptionIds.every((d) => updated[d]);

      setSelectedOfferings((prevOfferings) => ({
        ...prevOfferings,
        [offeringId]: allSelected,
      }));

      return updated;
    });
  };

  const handleSubmit = async () => {
    const selectedOfferingIds = Object.entries(selectedOfferings)
      .filter(([_, isChecked]) => isChecked)
      .map(([n]) => Number(n));

    const selectedOptionIds = Object.entries(selectedOptions)
      .filter(([_, isChecked]) => isChecked)
      .map(([m]) => Number(m));

    try {
      await OfferingQuery.saveAddons(
        id,
        selectedOfferingIds,
        selectedOptionIds,
      );
      toast({
        title: 'Success',
        description: 'Product Add-on updated successfully.',
        variant: 'success',
      });
      await getQueryClient().invalidateQueries({
        queryKey: ['AddOn', { id }],
      });
    } catch (err) {
      console.error('Error saving add-ons:', err);
      toast({
        title: 'Error',
        description: 'Failed to save add-ons.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="add-on-section">
      <Accordion type="multiple" className="space-y-4">
        {allAddOn?.map((category) => (
          <AccordionItem
            key={category.categoryId}
            value={`category-${category.categoryId}`}
          >
            <AccordionTrigger>
              <h4 className="text-sm font-semibold text-gray-700">
                {category.categoryName}
              </h4>
            </AccordionTrigger>
            <AccordionContent>
              {category.offerings && category.offerings.length > 0 ? (
                <Accordion type="multiple" className="pl-6">
                  {category.offerings.map((offering) => {
                    const optionIds = offering.options?.map((o) => o.id) ?? [];
                    return (
                      <AccordionItem
                        key={offering.id}
                        value={`offering-${offering.id}`}
                      >
                        <AccordionTrigger className="text-sm font-medium text-gray-800 flex items-center">
                          <label className="mr-2">
                            <input
                              type="checkbox"
                              name={`offering-${offering.id}`}
                              value={offering.id}
                              className="mt-0.5 w-4 h-4"
                              checked={!!selectedOfferings[offering.id]}
                              onChange={() =>
                                handleOfferingChange(offering.id, optionIds)
                              }
                            />
                          </label>
                          <span>{offering.name}</span>
                        </AccordionTrigger>
                        <AccordionContent>
                          {offering.options && offering.options.length > 0 ? (
                            <ul className="space-y-1 pl-8">
                              {offering.options?.map((option, index) => (
                                <li
                                  key={option.id ?? index}
                                  className="flex items-start space-x-2"
                                >
                                  <input
                                    type="checkbox"
                                    name={`offering-${offering.id}-option-${index}`}
                                    value={option.id}
                                    className="mt-1"
                                    checked={!!selectedOptions[option.id]}
                                    onChange={() =>
                                      handleOptionChange(
                                        offering.id,
                                        option.id,
                                        optionIds,
                                      )
                                    }
                                  />
                                  <span className="text-sm text-gray-600">
                                    {option.name}
                                  </span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <div className="text-sm italic text-gray-400">
                              No options available
                            </div>
                          )}
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
                </Accordion>
              ) : (
                <div className="text-sm italic text-gray-400">
                  No offerings available
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
      <div className="flex justify-between pt-6 border-t border-slate-200">
        <Button
          type="button"
          variant="outline"
          onClick={() => push('/dashboard/setup/products-services/product')}
        >
          Cancel
        </Button>
        <Button variant="main" type="submit" onClick={handleSubmit}>
          Save
        </Button>
      </div>
    </div>
  );
}

function AddOnSection({ id }: IAddOnSection) {
  const {
    data: addon,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['AddOn', { id }],
    queryFn: () => OfferingQuery.getAddon(Number(id)),
    enabled: !!id,
    select: (res) => {
      return {
        selectedOfferings: res.selectedOfferings ?? [],
        selectedOptions: res.selectedOptions ?? [],
      };
    },
  });

  const { data: allAddOn, isLoading: loadingAddOn } = useQuery({
    queryKey: ['Addon'],
    queryFn: () => CategoryQuery.getAllAddOn(),
  });

  const isFormReady = !isLoading && !loadingAddOn;

  return (
    <Suspense isLoading={isLoading || loadingAddOn}>
      {isLoading || loadingAddOn ? (
        <Spinner />
      ) : isError ? (
        <div className="text-red-500">Error loading data: {error?.message}</div>
      ) : (
        <FormContent defaultValues={addon} id={id} allAddOn={allAddOn} />
      )}
    </Suspense>
  );
}

export default AddOnSection;

import {
  GroundCondition,
  GroundConditionCreate,
} from '@/models/GroundCondition';
import fetcher from './fetcher';

const GroundConditionQuery = {
  getAll: async () => fetcher<GroundCondition[]>('GroundConditions'),
  getById: async (id: number) =>
    fetcher<GroundCondition>(`GroundConditions/${id}`),
  create: async (data: GroundConditionCreate) =>
    fetcher('GroundConditions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  update: (id: number) => async (data: GroundConditionCreate) =>
    fetcher(`GroundConditions/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  delete: async (id: number) =>
    fetcher(`GroundConditions/${id}`, { method: 'DELETE' }),
};

export default GroundConditionQuery;

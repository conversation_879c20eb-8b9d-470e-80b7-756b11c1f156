'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowLeft,
  Grip,
  LayoutGrid,
  LogOut,
  Plus,
  Save,
  Search,
  Settings,
  X,
  Home,
  FileText,
  Users,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { Switch } from '@/components/ui/switch';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { toast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Define menu item type
interface MenuItem {
  id: number;
  name: string;
  path: string;
  icon: string;
  order: number;
  active: boolean;
  lastUpdatedBy: string;
  lastUpdatedDate: string;
}

export default function HorizontalMenuView() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newMenuItem, setNewMenuItem] = useState<Omit<MenuItem, 'id'>>({
    name: '',
    path: '',
    icon: 'LayoutGrid',
    order: 1,
    active: true,
    lastUpdatedBy: 'Current User',
    lastUpdatedDate: new Date().toISOString().split('T')[0],
  });

  // Sample horizontal menu items
  const [menuItems, setMenuItems] = useState<MenuItem[]>([
    {
      id: 1,
      name: 'MANAGEMENT',
      path: '/',
      icon: 'LayoutGrid',
      order: 1,
      active: true,
      lastUpdatedBy: 'System Admin',
      lastUpdatedDate: '2023-12-15',
    },
    {
      id: 2,
      name: 'SETUP',
      path: '/setup',
      icon: 'Settings',
      order: 2,
      active: true,
      lastUpdatedBy: 'System Admin',
      lastUpdatedDate: '2023-12-15',
    },
    {
      id: 3,
      name: 'LOGOUT',
      path: '/login',
      icon: 'LogOut',
      order: 3,
      active: true,
      lastUpdatedBy: 'System Admin',
      lastUpdatedDate: '2023-12-15',
    },
  ]);

  // Filter menu items based on search term
  const filteredMenuItems = menuItems
    .filter((item) => {
      return (
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.path.toLowerCase().includes(searchTerm.toLowerCase())
      );
    })
    .sort((a, b) => a.order - b.order);

  // Function to render icon based on icon name
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'LayoutGrid':
        return <LayoutGrid className="h-4 w-4" />;
      case 'Settings':
        return <Settings className="h-4 w-4" />;
      case 'LogOut':
        return <LogOut className="h-4 w-4" />;
      case 'FileText':
        return <FileText className="h-4 w-4" />;
      case 'Users':
        return <Users className="h-4 w-4" />;
      case 'Home':
        return <Home className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Handle drag end
  const handleDragEnd = (result: any) => {
    setIsDragging(false);

    // Drop outside the list
    if (!result.destination) {
      return;
    }

    // If position didn't change
    if (result.destination.index === result.source.index) {
      return;
    }

    // Reorder the items
    const reorderedItems = Array.from(filteredMenuItems);
    const [removed] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, removed);

    // Update order numbers
    const updatedItems = reorderedItems.map((item, index) => ({
      ...item,
      order: index + 1,
    }));

    // Update all menu items with new order
    const newMenuItems = menuItems.map((item) => {
      const updatedItem = updatedItems.find(
        (updated) => updated.id === item.id,
      );
      return updatedItem || item;
    });

    setMenuItems(newMenuItems);
    setHasChanges(true);
  };

  // Handle drag start
  const handleDragStart = () => {
    setIsDragging(true);
  };

  // Handle save changes
  const handleSaveChanges = () => {
    // In a real app, you would save the changes to the server here
    toast({
      title: 'Changes saved',
      description: 'Menu order has been updated successfully',
    });
    setHasChanges(false);
  };

  // Toggle active state
  const toggleActive = (id: number) => {
    setMenuItems(
      menuItems.map((item) =>
        item.id === id ? { ...item, active: !item.active } : item,
      ),
    );
    setHasChanges(true);
  };

  const handleAddItem = () => {
    // Reset the form
    setNewMenuItem({
      name: '',
      path: '',
      icon: 'LayoutGrid',
      order: menuItems.length + 1,
      active: true,
      lastUpdatedBy: 'Current User',
      lastUpdatedDate: new Date().toISOString().split('T')[0],
    });
    setIsAddDialogOpen(true);
  };

  const handleSaveNewItem = () => {
    // Validate required fields
    if (!newMenuItem.name.trim()) {
      toast({
        title: 'Error',
        description: 'Menu item name is required',
        variant: 'destructive',
      });
      return;
    }

    // Create new menu item with a unique ID
    const newItem: MenuItem = {
      ...newMenuItem,
      id: Math.max(...menuItems.map((item) => item.id), 0) + 1,
    };

    // Add to menu items
    setMenuItems([...menuItems, newItem]);
    setIsAddDialogOpen(false);
    setHasChanges(true);

    toast({
      title: 'Menu item added',
      description: `"${newItem.name}" has been added to the menu`,
    });
  };

  return (
    <div>
      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="outline"
          size="sm"
          className="gap-1"
          onClick={() => router.push('/setup/menu-management')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <h1 className="text-2xl font-bold text-slate-800">Horizontal Menu</h1>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <CardTitle>Menu Items</CardTitle>
          <div className="flex gap-2">
            {hasChanges && (
              <Button
                className="bg-[#00646C] hover:bg-[#00646C]/90"
                onClick={handleSaveChanges}
              >
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            )}
            <Button
              className="bg-[#00646C] hover:bg-[#00646C]/90"
              onClick={handleAddItem}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Menu Item
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4">
            <div className="relative w-full md:w-64">
              <Input
                type="text"
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
            </div>
            <div className="text-sm text-slate-500">
              {isDragging ? (
                <span className="text-[#00646C] font-medium">
                  Drag to reorder menu items...
                </span>
              ) : (
                <span>Drag items to change their order</span>
              )}
            </div>
          </div>

          <div className="rounded-md border">
            <DragDropContext
              onDragEnd={handleDragEnd}
              onDragStart={handleDragStart}
            >
              <Table>
                <TableHeader>
                  <TableRow className="bg-slate-50">
                    <TableHead className="w-10"></TableHead>
                    <TableHead className="font-medium">ID</TableHead>
                    <TableHead className="font-medium">Icon</TableHead>
                    <TableHead className="font-medium">Name</TableHead>
                    <TableHead className="font-medium">Path</TableHead>
                    <TableHead className="font-medium">Order</TableHead>
                    <TableHead className="font-medium">Active</TableHead>
                    <TableHead className="font-medium">
                      Last Updated By
                    </TableHead>
                    <TableHead className="font-medium">Last Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <Droppable droppableId="menu-items">
                  {(provided) => (
                    <TableBody
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                    >
                      {filteredMenuItems.map((item, index) => (
                        <Draggable
                          key={item.id.toString()}
                          draggableId={item.id.toString()}
                          index={index}
                        >
                          {(draggableProvided, snapshot) => (
                            <TableRow
                              ref={draggableProvided.innerRef}
                              {...draggableProvided.draggableProps}
                              className={`${index % 2 === 1 ? 'bg-slate-50' : ''} ${
                                snapshot.isDragging
                                  ? 'bg-slate-100 opacity-80'
                                  : ''
                              }`}
                            >
                              <TableCell
                                {...draggableProvided.dragHandleProps}
                                className="cursor-grab"
                              >
                                <div className="flex items-center justify-center">
                                  <Grip className="h-4 w-4 text-slate-400" />
                                </div>
                              </TableCell>
                              <TableCell>{item.id}</TableCell>
                              <TableCell>
                                <div className="flex items-center justify-center text-slate-500">
                                  {renderIcon(item.icon)}
                                </div>
                              </TableCell>
                              <TableCell className="font-medium">
                                {item.name}
                              </TableCell>
                              <TableCell className="font-mono text-xs">
                                {item.path}
                              </TableCell>
                              <TableCell>{item.order}</TableCell>
                              <TableCell>
                                <Switch
                                  checked={item.active}
                                  onCheckedChange={() => toggleActive(item.id)}
                                />
                              </TableCell>
                              <TableCell>{item.lastUpdatedBy}</TableCell>
                              <TableCell>{item.lastUpdatedDate}</TableCell>
                            </TableRow>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                      {filteredMenuItems.length === 0 && (
                        <TableRow>
                          <TableCell
                            colSpan={9}
                            className="text-center py-4 text-slate-500"
                          >
                            No menu items found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  )}
                </Droppable>
              </Table>
            </DragDropContext>
          </div>

          <div className="mt-4 text-sm text-slate-500">
            Total: {filteredMenuItems.length} menu items
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Menu Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-b border-slate-200 mb-4">
            <div className="flex flex-wrap">
              {menuItems
                .filter((item) => item.active)
                .sort((a, b) => a.order - b.order)
                .map((item) => (
                  <div
                    key={item.id}
                    className="h-12 px-6 rounded-none text-sm font-medium transition-colors border-b-2 border-[#00646C] text-[#00646C] flex items-center"
                  >
                    {renderIcon(item.icon)}
                    <span className="ml-2">{item.name}</span>
                  </div>
                ))}
            </div>
          </div>
          <p className="text-sm text-slate-500">
            This is a preview of how the horizontal menu currently appears in
            the application.
            {hasChanges && (
              <span className="text-[#00646C] ml-1">(Unsaved changes)</span>
            )}
          </p>
        </CardContent>
      </Card>
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Add Menu Item</DialogTitle>
            <DialogDescription>
              Create a new menu item. Fill in the details below and click Save
              when you're done.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={newMenuItem.name}
                onChange={(e) =>
                  setNewMenuItem({ ...newMenuItem, name: e.target.value })
                }
                className="col-span-3"
                placeholder="e.g., REPORTS"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="path" className="text-right">
                Path <span className="text-red-500">*</span>
              </Label>
              <Input
                id="path"
                value={newMenuItem.path}
                onChange={(e) =>
                  setNewMenuItem({ ...newMenuItem, path: e.target.value })
                }
                className="col-span-3"
                placeholder="e.g., /reports"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="icon" className="text-right">
                Icon
              </Label>
              <Select
                value={newMenuItem.icon}
                onValueChange={(value) =>
                  setNewMenuItem({ ...newMenuItem, icon: value })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select an icon" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LayoutGrid">Layout Grid</SelectItem>
                  <SelectItem value="Settings">Settings</SelectItem>
                  <SelectItem value="LogOut">Log Out</SelectItem>
                  <SelectItem value="FileText">File Text</SelectItem>
                  <SelectItem value="Users">Users</SelectItem>
                  <SelectItem value="Home">Home</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="order" className="text-right">
                Order
              </Label>
              <Input
                id="order"
                type="number"
                min="1"
                value={newMenuItem.order}
                onChange={(e) =>
                  setNewMenuItem({
                    ...newMenuItem,
                    order: Number(e.target.value) || 1,
                  })
                }
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="active" className="text-right">
                Active
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="active"
                  checked={newMenuItem.active}
                  onCheckedChange={(checked) =>
                    setNewMenuItem({ ...newMenuItem, active: checked })
                  }
                />
                <Label htmlFor="active" className="font-normal">
                  {newMenuItem.active ? 'Visible' : 'Hidden'}
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              className="bg-[#00646C] hover:bg-[#00646C]/90"
              onClick={handleSaveNewItem}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Menu Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

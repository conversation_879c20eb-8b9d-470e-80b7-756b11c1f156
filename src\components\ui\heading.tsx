'use client';
import { useRouter } from '@/utils/navigation';
import { Button } from './button';

interface HeadingProps {
  title: string;
  description?: string;
}

export const Heading: React.FC<HeadingProps> = ({ title, description }) => {
  const router = useRouter();
  const handleBackClick = () => {
    router.back();
  };

  return (
    <div className="flex flex-row gap-2 border-b border-foreground items-center mb-2 w-[50%] pb-2">
      <Button variant="outline" size="sm" onClick={handleBackClick}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-5 h-5 text-main"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 12H5"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 5l-7 7 7 7"
          />
        </svg>
        Back
      </Button>
      <div className="flex flex-col justify-center w-full">
        <h2 className="text-main text-2xl font-bold tracking-tight">{title}</h2>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
    </div>
  );
};

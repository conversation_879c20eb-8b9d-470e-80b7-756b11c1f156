'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { Suspense } from 'react';

import { getQueryClient } from '@/utils/query-client';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import ShowQuery from '@/services/queries/ShowQuery';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import ShowContactQuery from '@/services/queries/ShowContactQuery';

interface HallContactTabProps {
  showId?: number;
  onSuccess?: () => void;
}

const HallContactSchema = z.object({
  hallId: z.string().min(1, 'Hall is required'),
  contactId: z.string().min(1, 'Contact is required'),
});

function FormContent({
  defaultValues,
  showId,
  onSuccess,
}: {
  defaultValues?: any;
  showId?: number;
  onSuccess?: () => void;
}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const isEditMode = !!showId;

  // Get location ID from the show data to fetch halls and contacts
  const { data: showData } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: !!showId,
  });

  const { data: halls } = useQuery({
    queryKey: ['ShowHall', showData?.locationId],
    queryFn: () => ShowHallQuery.getBriefByLocation(showData!.locationId),
    enabled: !!showData?.locationId,
  });

  const { data: contacts } = useQuery({
    queryKey: ['ShowContact', showData?.locationId],
    queryFn: () => ShowContactQuery.getByLocation(showData!.locationId),
    enabled: !!showData?.locationId,
  });

  const form = useForm<any>({
    resolver: zodResolver(HallContactSchema),
    mode: 'onChange',
    defaultValues: defaultValues ?? {
      hallId: '',
      contactId: '',
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: any) => {
      const formattedData = {
        hallId: Number(data.hallId),
        contactId: Number(data.contactId),
      };

      if (isEditMode) {
        await ShowQuery.updateHallContact(showId!)(formattedData);
      } else {
        throw new Error('Hall and Contact can only be set in edit mode');
      }
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Hall and contact updated successfully',
      });
      queryClient.invalidateQueries({ queryKey: ['Shows', showId, 'hall'] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update hall and contact',
        variant: 'destructive',
      });
    },
  });

  const hallOptions =
    halls?.map((hall) => ({
      label: `${hall.name} (${hall.id})`,
      value: hall.id.toString(),
    })) || [];

  const contactOptions =
    contacts?.map((contact) => ({
      label:
        `${contact.firstName || ''} ${contact.lastName || ''} - ${contact.email || ''}`.trim(),
      value: contact.id.toString(),
    })) || [];

  if (!isEditMode) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Hall and Contact Assignment
          </h3>
          <p className="text-gray-500">
            Hall and contact can only be assigned after the show is created.
            Please save the general information first.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              Hall and Contact Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Field
                control={form.control}
                name="hallId"
                label="Hall"
                type={{
                  type: 'select',
                  props: {
                    options: hallOptions,
                    placeholder:
                      hallOptions.length > 0
                        ? 'Select Hall'
                        : 'No halls available',
                  },
                }}
                required
              />

              <Field
                control={form.control}
                name="contactId"
                label="Contact"
                type={{
                  type: 'select',
                  props: {
                    options: contactOptions,
                    placeholder:
                      contactOptions.length > 0
                        ? 'Select Contact'
                        : 'No contacts available',
                  },
                }}
                required
              />
            </div>

            {/* Display current hall and contact info if available */}
            {defaultValues && (
              <div className="mt-6 p-4 bg-slate-50 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-3">
                  Current Assignment
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Hall:</span>
                    <p className="text-gray-600">
                      {defaultValues.hallName} ({defaultValues.hallCode})
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Contact:</span>
                    <p className="text-gray-600">{defaultValues.contactName}</p>
                    <p className="text-gray-500 text-xs">
                      {defaultValues.contactEmail} |{' '}
                      {defaultValues.contactPhone}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Updating...' : 'Update Hall & Contact'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

export default function HallContactTab({
  showId,
  onSuccess,
}: HallContactTabProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Shows', showId, 'hall'],
    queryFn: () => ShowQuery.getHallContact(showId!),
    enabled: !!showId,
    select: (res) => ({
      hallId: res.hallId ? res.hallId.toString() : '',
      contactId: res.contactId ? res.contactId.toString() : '',
      hallName: res.hallName || '',
      hallCode: res.hallCode || '',
      contactName: res.contactName || '',
      contactEmail: res.contactEmail || '',
      contactPhone: res.contactPhone || '',
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} showId={showId} onSuccess={onSuccess} />
    </Suspense>
  );
}

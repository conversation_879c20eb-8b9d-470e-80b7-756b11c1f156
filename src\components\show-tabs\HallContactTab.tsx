'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';

import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import ShowQuery from '@/services/queries/ShowQuery';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import ShowContactQuery from '@/services/queries/ShowContactQuery';

interface HallContactTabProps {
  showId?: number;
  onSuccess?: () => void;
}

const HallContactSchema = z.object({
  hallId: z.string().min(1, 'Hall is required'),
  contactId: z.string().min(1, 'Contact is required'),
});

function FormContent({
  defaultValues,
  showId,
  onSuccess,
}: {
  defaultValues?: any;
  showId?: number;
  onSuccess?: () => void;
}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const isEditMode = !!showId;

  // Get location ID from the show data to fetch halls and contacts
  const { data: showData, isLoading: isLoadingShowData } = useQuery({
    queryKey: ['Shows', showId],
    queryFn: () => ShowQuery.getOne(showId!),
    enabled: !!showId,
  });

  const { data: halls, isLoading: isLoadingHalls } = useQuery({
    queryKey: ['ShowHall', showData?.locationId],
    queryFn: () => ShowHallQuery.getBriefByLocation(showData!.locationId),
    enabled: !!showData?.locationId,
  });

  const { data: contacts, isLoading: isLoadingContacts } = useQuery({
    queryKey: ['ShowContact', showData?.locationId],
    queryFn: () => ShowContactQuery.getByLocation(showData!.locationId),
    enabled: !!showData?.locationId,
  });

  const form = useForm<any>({
    resolver: zodResolver(HallContactSchema),
    mode: 'onChange',
    defaultValues: defaultValues ?? {
      hallId: '',
      contactId: '',
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: any) => {
      const formattedData = {
        hallId: Number(data.hallId),
        contactId: Number(data.contactId),
      };

      if (isEditMode) {
        await ShowQuery.updateHallContact(showId!)(formattedData);
      } else {
        throw new Error('Hall and Contact can only be set in edit mode');
      }
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Hall and contact updated successfully',
      });
      queryClient.invalidateQueries({ queryKey: ['Shows', showId, 'hall'] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update hall and contact',
        variant: 'destructive',
      });
    },
  });

  const hallOptions =
    halls?.map((hall) => ({
      label: `${hall.name} (${hall.id})`,
      value: hall.id.toString(),
    })) || [];

  const contactOptions =
    contacts?.map((contact) => ({
      label:
        `${contact.firstName || ''} ${contact.lastName || ''} - ${contact.email || ''}`.trim(),
      value: contact.id.toString(),
    })) || [];

  const isDataLoading =
    isLoadingHalls || isLoadingContacts || isLoadingShowData;

  if (!isEditMode) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Hall and Contact Assignment
          </h3>
          <p className="text-gray-500">
            Hall and contact can only be assigned after the show is created.
            Please save the general information first.
          </p>
        </div>
      </div>
    );
  }

  // Show loading state while fetching halls and contacts
  if (isDataLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <Spinner />
          <p className="text-gray-500 mt-2">Loading halls and contacts...</p>
        </div>
      </div>
    );
  }

  // Show message if no location data is available
  if (!showData?.locationId) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Location Information
          </h3>
          <p className="text-gray-500">
            Unable to load halls and contacts. Please ensure the show has a
            valid location assigned.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              Hall and Contact Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Field
                  control={form.control}
                  name="hallId"
                  label="Hall"
                  type={{
                    type: 'select',
                    props: {
                      options: hallOptions,
                      placeholder: isDataLoading
                        ? 'Loading halls...'
                        : hallOptions.length > 0
                          ? 'Select Hall'
                          : 'No halls available',
                    },
                  }}
                  required
                />
                {!isDataLoading && hallOptions.length === 0 && (
                  <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
                    <p className="text-sm text-amber-800">
                      No halls available for this location. Please add halls in
                      Hall Management first.
                    </p>
                  </div>
                )}
              </div>

              <div>
                <Field
                  control={form.control}
                  name="contactId"
                  label="Contact"
                  type={{
                    type: 'select',
                    props: {
                      options: contactOptions,
                      placeholder: isDataLoading
                        ? 'Loading contacts...'
                        : contactOptions.length > 0
                          ? 'Select Contact'
                          : 'No contacts available',
                    },
                  }}
                  required
                />
                {!isDataLoading && contactOptions.length === 0 && (
                  <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
                    <p className="text-sm text-amber-800">
                      No contacts available for this location. Please add
                      contacts in Contact Management first.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={
                isPending ||
                hallOptions.length === 0 ||
                contactOptions.length === 0
              }
            >
              {isPending ? 'Saving...' : 'Save & Continue'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

export default function HallContactTab({
  showId,
  onSuccess,
}: HallContactTabProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Shows', showId, 'hall'],
    queryFn: () => ShowQuery.getHallContact(showId!),
    enabled: !!showId,
    select: (res) => ({
      // Form fields
      hallId: res.hallId ? res.hallId.toString() : '',
      contactId: res.contactId ? res.contactId.toString() : '',
      // Display information
      hallName: res.hallName || '',
      hallCode: res.hallCode || '',
      contactName: res.contactName || '',
      contactEmail: res.contactEmail || '',
      contactPhone: res.contactPhone || '',
      // Flag to show if there's existing assignment
      hasAssignment: !!(res.hallId && res.contactId),
    }),
  });

  if (isLoading && !isPaused) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <FormContent defaultValues={data} showId={showId} onSuccess={onSuccess} />
  );
}

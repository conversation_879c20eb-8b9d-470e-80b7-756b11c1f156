'use client';

import { Button } from '@/components/ui/button';

interface HallDetailsTabProps {
  showId?: number;
  onSuccess?: (newShowId?: number) => void;
}

export default function HallDetailsTab({
  showId,
  onSuccess,
}: HallDetailsTabProps) {
  const handleContinue = () => {
    // For now, just call onSuccess to move to next tab
    // In the future, this will submit the hall details form
    onSuccess?.();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
          Hall Details
        </h2>

        <div className="min-h-[400px] flex items-center justify-center">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-slate-800 mb-2">
              Hall Details
            </h3>
            <p className="text-slate-500 mb-4">
              This section is under development. Future API endpoints will be
              integrated here.
            </p>
            <p className="text-sm text-slate-400">
              Show ID: {showId || 'Not available'}
            </p>
          </div>
        </div>

        <div className="flex justify-end mt-6 pt-4 border-t border-slate-200">
          <Button
            onClick={handleContinue}
            className="bg-[#00646C] hover:bg-[#00646C]/90"
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  ShowLocationGeneralData,
  ShowLocationGeneralSchema,
} from '@/schema/ShowLocationSchema';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { ChevronRight } from 'lucide-react';
import { DropzoneOptions } from 'react-dropzone';

interface ShowFacilityGeneralInfoProps {
  id?: number;
}

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: ShowLocationGeneralData;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();

  const dropzone = {
    accept: {
      'application/pdf': ['.pdf'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const form = useForm<ShowLocationGeneralData>({
    resolver: zodResolver(ShowLocationGeneralSchema),
    defaultValues: defaultValues ?? {
      locationCode: '',
      name: '',
      telephone: '',
      tollfree: '',
      fax: '',
      mapLink: '',
      website: '',
      email: '',
      accessPlan: undefined,
      isArchived: false,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: id
      ? (data: ShowLocationGeneralData) =>
          ShowLocationQuery.editGeneral(id, data)
      : (data: ShowLocationGeneralData) => ShowLocationQuery.addGeneral(data),
    onSuccess: (result) => {
      const newId = typeof result === 'number' ? result : id;
      if (newId) {
        push(`/dashboard/setup/show-facility/${newId}/address`);
      }
      toast({
        title: 'Success',
        description: id
          ? 'Facility updated successfully.'
          : 'Facility created successfully.',
        variant: 'success',
      });
    },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          General Information
        </h2>

        {/* Form Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32 gap-y-2">
          <Field
            control={form.control}
            name="name"
            label="Facility Name"
            placeholder="Enter facility name"
            type="text"
            required
          />
          <Field
            control={form.control}
            name="locationCode"
            label="Location Code"
            placeholder="Enter location code"
            type="text"
            required
          />
          <Field
            control={form.control}
            name="telephone"
            label="Telephone"
            type="text"
            placeholder="Enter phone number"
          />
          <Field
            control={form.control}
            name="email"
            label="Email"
            placeholder="Enter email address"
            type="text"
          />
        </div>
        <Field
          control={form.control}
          name="mapLink"
          label="Map Link"
          placeholder="Enter map link"
          type="text"
        />
        <Field
          control={form.control}
          name="website"
          label="Website"
          placeholder="Enter website link"
          type="text"
        />
        <Field
          control={form.control}
          name="accessPlan"
          label={id ? 'Replace Access Plan' : 'Access Plan'}
          required={id ? true : false}
          type={{
            type: 'file',
            props: {
              dropzoneOptions: dropzone,
            },
          }}
          placeholder="How to access the facility?"
        />
        {id && (
          <Field
            control={form.control}
            name="isArchived"
            label="Archived"
            type="checkbox"
          />
        )}
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button variant="outline">Cancel</Button>
          <Button variant="main" type="submit" disabled={isPending}>
            {isPending ? <Spinner className="mr-2" /> : null}
            Save & Continue
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function ShowFacilityGeneralInfo({
  id,
}: ShowFacilityGeneralInfoProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['ShowLocation', { id }],
    queryFn: () => ShowLocationQuery.get(id!),

    enabled: !!id,
    select: (res: ShowLocationGeneralData) =>
      ({
        locationCode: res.locationCode ?? '',
        name: res.name ?? '',
        telephone: res.telephone ?? '',
        tollfree: res.tollfree ?? '',
        fax: res.fax ?? '',
        mapLink: res.mapLink ?? '',
        website: res.website ?? '',
        email: res.email ?? '',
        accessPlan: res.accessPlan,
        accessPlanPath: res.accessPlanPath ?? '',
        isArchived: res.isArchived ?? false,
      }) as ShowLocationGeneralData,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} />
    </Suspense>
  );
}

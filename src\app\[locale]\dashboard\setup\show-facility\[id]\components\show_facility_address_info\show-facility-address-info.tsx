'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  ShowLocationAddressData,
  ShowLocationAddressSchema,
} from '@/schema/ShowLocationSchema';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { ChevronRight } from 'lucide-react';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';

interface ShowFacilityAddressInfoProps {
  id?: number;
  defaultValues: ShowLocationAddressData;
}

function FormContent({ id, defaultValues }: ShowFacilityAddressInfoProps) {
  const { push } = useRouter();
  const { toast } = useToast();

  const form = useForm<ShowLocationAddressData>({
    resolver: zodResolver(ShowLocationAddressSchema),
    defaultValues: {
      id: Number(id),
      address1: defaultValues.address1 ?? '',
      address2: defaultValues.address2 ?? '',
      city: defaultValues.city ?? '',
      provinceId: defaultValues.provinceId?.toString() ?? undefined,
      postalCode: defaultValues.postalCode ?? '',
      countryId: defaultValues.countryId?.toString() ?? undefined,
      shippingAddress1: defaultValues.shippingAddress1 ?? '',
      shippingAddress2: defaultValues.shippingAddress2 ?? '',
      shippingCity: defaultValues.shippingCity ?? '',
      shippingProvinceId:
        defaultValues.shippingProvinceId?.toString() ?? undefined,
      shippingPostalCode: defaultValues.shippingPostalCode ?? '',
      shippingCountryId:
        defaultValues.shippingCountryId?.toString() ?? undefined,
      sameForShipping: defaultValues.sameForShipping ?? false,
    },
  });

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (data: ShowLocationAddressData) =>
      ShowLocationQuery.updateAddress(Number(id), data),
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Address updated successfully.',
        variant: 'success',
      });
      push(`/dashboard/setup/show-facility/${id}/halls`); // adjust path if needed
    },
  });

  const sameForShipping = form.watch('sameForShipping');

  useEffect(() => {
    if (sameForShipping) {
      const values = form.getValues();
      form.setValue('shippingAddress1', values.address1);
      form.setValue('shippingAddress2', values.address2);
      form.setValue('shippingCity', values.city);
      form.setValue('shippingPostalCode', values.postalCode);
      form.setValue('shippingProvinceId', values.provinceId);
      form.setValue('shippingCountryId', values.countryId);
    }
  }, [sameForShipping, form]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          Address Information
        </h2>

        {/* Primary Address Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-2">
          <Field
            control={form.control}
            name="address1"
            label="Address 1"
            type="text"
            placeholder="Enter Address"
          />
          <Field
            control={form.control}
            name="address2"
            label="Address 2"
            type="text"
            placeholder="Enter Address"
          />
          <Field
            control={form.control}
            name="city"
            label="City"
            type="text"
            placeholder="Enter City"
          />
          <Field
            control={form.control}
            name="postalCode"
            label="Postal Code"
            type="text"
            placeholder="Enter Postal Code"
          />
          <Field
            control={form.control}
            name="provinceId"
            label="Province"
            required
            type={{
              type: 'select',
              props: {
                options:
                  provinces?.map((p) => ({
                    label: p.name,
                    value: p.id.toString(),
                  })) ?? [],
                placeholder: 'Select a Province',
              },
            }}
          />
          <Field
            control={form.control}
            name="countryId"
            label="Country"
            required
            type={{
              type: 'select',
              props: {
                options:
                  countries?.map((c) => ({
                    label: c.name,
                    value: c.id.toString(),
                  })) ?? [],
                placeholder: 'Select a Country',
              },
            }}
          />
        </div>

        <h3 className="text-lg font-medium text-[#00646C] mt-8">
          Shipping Address
        </h3>
        <Field
          control={form.control}
          name="sameForShipping"
          label="Using Primary Address"
          type="checkbox"
        />

        {!sameForShipping && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-2">
            <Field
              control={form.control}
              name="shippingAddress1"
              label="Shipping Address 1"
              type="text"
              placeholder="Enter Shipping Address"
            />
            <Field
              control={form.control}
              name="shippingAddress2"
              label="Shipping Address 2"
              type="text"
              placeholder="Enter Shipping Address"
            />
            <Field
              control={form.control}
              name="shippingCity"
              label="Shipping City"
              type="text"
              placeholder="Enter Shipping City"
            />
            <Field
              control={form.control}
              name="shippingPostalCode"
              label="Shipping Postal Code"
              type="text"
              placeholder="Enter Shipping Postal Code"
            />
            <Field
              control={form.control}
              name="shippingProvinceId"
              label="Shipping Province"
              type={{
                type: 'select',
                props: {
                  options:
                    provinces?.map((p) => ({
                      label: p.name,
                      value: p.id.toString(),
                    })) ?? [],
                  placeholder: 'Select a Province',
                },
              }}
            />
            <Field
              control={form.control}
              name="shippingCountryId"
              label="Shipping Country"
              type={{
                type: 'select',
                props: {
                  options:
                    countries?.map((c) => ({
                      label: c.name,
                      value: c.id.toString(),
                    })) ?? [],
                  placeholder: 'Select a Country',
                },
              }}
            />
          </div>
        )}

        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button variant="outline">Cancel</Button>
          <Button variant="main" type="submit" disabled={isPending}>
            {isPending ? <Spinner className="mr-2" /> : null}
            Save & Continue
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function ShowFacilityAddressInfo({ id }: { id: number }) {
  const {
    data: item,
    isPaused,
    isLoading,
  } = useQuery({
    queryKey: ['ShowLocationAddress', { id }],
    queryFn: () => ShowLocationQuery.getAddressById(id),
    enabled: !!id,
    select: (res: ShowLocationAddressData) =>
      ({
        id,
        address1: res.address1 ?? '',
        address2: res.address2 ?? '',
        postalCode: res.postalCode ?? '',
        city: res.city ?? '',
        provinceId: res.provinceId ?? undefined,
        countryId: res.countryId ?? undefined,
        shippingAddress1: res.shippingAddress1 ?? '',
        shippingAddress2: res.shippingAddress2 ?? '',
        shippingPostalCode: res.shippingPostalCode ?? '',
        shippingCity: res.shippingCity ?? '',
        shippingProvinceId: res.shippingProvinceId ?? undefined,
        shippingCountryId: res.shippingCountryId ?? undefined,
        sameForShipping: res.sameForShipping ?? false,
      }) as ShowLocationAddressData,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {item && <FormContent id={id} defaultValues={item} />}
    </Suspense>
  );
}

'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';

// Define menu types
interface MenuType {
  id: number;
  name: string;
  description: string;
  lastUpdatedBy: string;
  lastUpdatedDate: string;
  itemCount: number;
  viewPath: string;
}

export default function EmptyMenuManagement() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');

  // Sample menu type data with numeric IDs and view paths
  const menuTypes: MenuType[] = [
    {
      id: 1,
      name: 'Horizontal Menu',
      description: 'Top navigation menu with main sections',
      lastUpdatedBy: 'System Admin',
      lastUpdatedDate: '2023-12-15',
      itemCount: 3,
      viewPath: '/setup/menu-management/horizontal-menu',
    },
    {
      id: 2,
      name: 'Sidebar Menu',
      description: 'Left sidebar navigation for setup section',
      lastUpdatedBy: 'Harry Hekimian',
      lastUpdatedDate: '2024-01-20',
      itemCount: 17,
      viewPath: '/setup/menu-management/sidebar-menu',
    },
    {
      id: 3,
      name: 'Tab Menu',
      description: 'Vertical tabs for add show process',
      lastUpdatedBy: 'System Admin',
      lastUpdatedDate: '2024-03-01',
      itemCount: 11,
      viewPath: '/setup/menu-management/tab-menu',
    },
  ];

  // Filter menu types based on search term
  const filteredMenuTypes = menuTypes.filter((type) => {
    return (
      type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      type.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Handle view button click
  const handleViewClick = (viewPath: string) => {
    router.push(viewPath);
  };

  return (
    <div>
      <h1 className="text-2xl font-bold text-slate-800 mb-6">
        Menu Management
      </h1>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Menu Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4">
            <div className="relative w-full md:w-64">
              <Input
                type="text"
                placeholder="Search menu types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className="bg-slate-50">
                  <TableHead className="font-medium">ID</TableHead>
                  <TableHead className="font-medium">Type</TableHead>
                  <TableHead className="font-medium">Description</TableHead>
                  <TableHead className="font-medium">Items</TableHead>
                  <TableHead className="font-medium">Last Updated By</TableHead>
                  <TableHead className="font-medium">Last Updated</TableHead>
                  <TableHead className="text-right font-medium">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMenuTypes.map((type, index) => (
                  <TableRow
                    key={type.id}
                    className={index % 2 === 1 ? 'bg-slate-50' : ''}
                  >
                    <TableCell className="font-medium">{type.id}</TableCell>
                    <TableCell className="font-medium">{type.name}</TableCell>
                    <TableCell>{type.description}</TableCell>
                    <TableCell>{type.itemCount}</TableCell>
                    <TableCell>{type.lastUpdatedBy}</TableCell>
                    <TableCell>{type.lastUpdatedDate}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-[#00646C] hover:text-[#00646C]/80"
                        onClick={() => handleViewClick(type.viewPath)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredMenuTypes.length === 0 && (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className="text-center py-4 text-slate-500"
                    >
                      No menu types found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <div className="mt-4 text-sm text-slate-500">
            Total: {filteredMenuTypes.length} menu types
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

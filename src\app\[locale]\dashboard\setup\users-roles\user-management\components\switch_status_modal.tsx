import { useMutation } from '@tanstack/react-query';

import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { cn } from '@/lib/utils';
import UsersQuery from '@/services/queries/UsersQuery';
import { getQueryClient } from '@/utils/query-client';
import { Button } from '@/components/ui/button';
interface ISwitchStatusModal {
  close: () => void;
  isChecked: boolean;
  userId: number;
}
export default function SwitchStatusModal({
  close,
  isChecked,
  userId,
}: ISwitchStatusModal) {
  const { mutate, isPending } = useMutation({
    mutationKey: UsersQuery.tags,
    mutationFn: UsersQuery.switchStatus,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });
      close();
    },
  });
  return (
    <ModalContainer
      onSubmit={(e) => {
        e.preventDefault();
        mutate(userId);
      }}
      className="gap-0"
      title={isChecked ? 'Archive User' : 'Unarchive User'}
      description={
        isChecked
          ? "This user will be archived and won't appear in the main list."
          : 'This user will be unarchived and will appear in the main list.'
      }
      controls={
        <div className="flex flex-row gap-2">
          <Button
            disabled={isPending}
            type="submit"
            className={cn({
              'bg-green-500': isChecked,
              'bg-destructive': !isChecked,
            })}
          >
            {isPending ? 'Please wait...' : isChecked ? 'Archive' : 'Unarchive'}
          </Button>
          <Button onClick={close} variant="secondary" disabled={isPending}>
            Cancel
          </Button>
        </div>
      }
    />
  );
}

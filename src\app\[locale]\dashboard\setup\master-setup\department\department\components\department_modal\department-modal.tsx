'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { getQueryClient } from '@/utils/query-client';
import Field from '@/components/ui/inputs/field';
import DepartmentQuery from '@/services/queries/DepartmentQuery';
import { BriefDataData, BriefDataSchema } from '@/schema/BriefDateSchema';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: BriefDataData;
  id?: number;
}) {
  const { toast } = useToast();

  const form = useForm<BriefDataData>({
    resolver: zodResolver(BriefDataSchema),
    defaultValues: {
      name: defaultValues?.name ?? '',
    },
  });

  // const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
  //   queryKey: ProvinceQuery.tags,
  //   queryFn: ProvinceQuery.getAll,
  // });

  // const { data: countries, isLoading: isLoadingCountries } = useQuery({
  //   queryKey: CountryQuery.tags,
  //   queryFn: CountryQuery.getAll,
  // });

  const { mutate } = useMutation({
    mutationFn: id ? DepartmentQuery.update(id) : DepartmentQuery.add,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: DepartmentQuery.tags,
      });
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Department Info', { id }],
        });
      }
      toast({
        title: 'Success',
        description: id
          ? 'Department updated successfully'
          : 'New department created',
        variant: 'success',
      });
      modal.close();
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        title={id ? 'Update Department' : 'Add Department'}
        description={
          id ? 'Update department details' : 'Create new department entry'
        }
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end w-full gap-4">
            <Button variant="main">{id ? 'Update' : 'Add'}</Button>
          </div>
        }
      >
        <div className="flex flex-col gap-2 mt-4">
          <Field
            control={form.control}
            name="name"
            label="Department Name"
            type="text"
            required
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

interface DepartmentModalProps {
  id?: number;
}

function DepartmentModal({ id }: DepartmentModalProps) {
  const {
    data: department,
    isPaused,
    isLoading,
  } = useQuery({
    queryKey: ['Department Info', { id }],
    queryFn: () => DepartmentQuery.get(Number(id!)),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent
          defaultValues={department}
          id={department ? Number(id) : undefined}
        />
      )}
    </Suspense>
  );
}

export default DepartmentModal;

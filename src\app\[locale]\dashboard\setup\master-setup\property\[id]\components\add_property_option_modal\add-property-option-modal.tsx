import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import PropertyOptionQuery from '@/services/queries/PropertyOptionQuery';
import { getQueryClient } from '@/utils/query-client';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  PropertyOptionUpdateData,
  PropertyOptionUpdateSchema,
} from '@/schema/PropertyOptionSchema';

function FormContent({
  defaultValues,
  propertyId,
  id,
}: {
  defaultValues?: PropertyOptionUpdateData;
  propertyId?: number;
  id?: number;
}) {
  const isEditMode = !!id;
  const { toast } = useToast();
  const form = useForm<PropertyOptionUpdateData>({
    resolver: zodResolver(PropertyOptionUpdateSchema),
    defaultValues: {
      propertyId: propertyId?.toString(),
      name: defaultValues?.name ?? '',
      description: defaultValues?.description ?? '',
      displayOrder: defaultValues?.displayOrder?.toString() ?? '0',
      code: defaultValues?.code ?? '',
    },
  });

  const { mutate } = useMutation({
    mutationFn: id ? PropertyOptionQuery.update(id) : PropertyOptionQuery.add,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: ['PropertyOptions', { propertyId: Number(propertyId) }],
      });

      toast({
        title: 'Success',
        description: id
          ? 'Property Option updated successfully'
          : 'New Property Option added successfully',
        variant: 'success',
      });

      modal.close();
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        className="w-full max-w-3xl"
        title={id ? 'Update Property Option' : 'Add Property Option'}
        description={
          id ? 'Update property option details' : 'Create new property option'
        }
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end w-full gap-4">
            <Button variant="main">{id ? 'Update' : 'Add'}</Button>
          </div>
        }
      >
        <div className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32 gap-y-2">
            <Field
              control={form.control}
              name="name"
              label="Property Name"
              placeholder="Enter name"
              type="text"
              required
            />
            <Field
              control={form.control}
              name="code"
              label="Property Code"
              placeholder="Code from 01–99"
              type="text"
            />
          </div>
          <Field
            control={form.control}
            name="displayOrder"
            label="Display Order"
            placeholder="Enter Display Order"
            type="number"
            required
          />
          <Field
            control={form.control}
            name="description"
            label="Description"
            placeholder="Enter description"
            type="textarea"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

interface AddPropertyOptionModalProps {
  propertyId?: number;
  propertyOptionId?: number;
}

function AddPropertyOptionModal({
  propertyId,
  propertyOptionId,
}: AddPropertyOptionModalProps) {
  const {
    data: propertyOption,
    isLoading,
    isPaused,
  } = useQuery({
    queryKey: [
      'PropertyOption',
      { propertyOptionId: Number(propertyOptionId) },
    ],
    queryFn: () => PropertyOptionQuery.get(Number(propertyOptionId!)),
    enabled: !!propertyOptionId,
    select: (data: PropertyOptionUpdateData) => {
      return {
        propertyId: data.propertyId,
        name: data?.name ?? '',
        code: data?.code ?? '',
        description: data?.description ?? '',
        displayOrder: data?.displayOrder ?? 0,
      } as PropertyOptionUpdateData;
    },
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent
          id={propertyOptionId}
          propertyId={propertyId}
          defaultValues={propertyOption}
        />
      )}
    </Suspense>
  );
}

export default AddPropertyOptionModal;

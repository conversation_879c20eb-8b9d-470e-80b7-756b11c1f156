import { CategoryData, CategoryUpdateData } from '@/schema/CategorySchema';
import fetcher from './fetcher';
import { CategoryDto, CategoryProperty } from '@/models/Category';
import { urlToFile } from '@/utils/file-helper';
import { PropertyDetail } from '@/models/Property';
import { CategoryWithOfferingsDto } from '@/models/Offering';

const CategoryQuery = {
  tags: ['Category'] as const,

  // GET: /Category
  getAll: async (): Promise<CategoryDto[]> => {
    return fetcher<CategoryDto[]>('Category');
  },

  // GET: /Category/{id}
  getById: async (id: number): Promise<CategoryUpdateData> => {
    const data = await fetcher<CategoryUpdateData>(`Category/${id}`);
    const image = await urlToFile('/images' + String(data.imagePath));
    return {
      ...data,
      image: image ? [image] : [],
    } satisfies CategoryUpdateData;
  },

  // GET: /Category/brief
  getBrief: async (): Promise<{ id: number; name: string }[]> => {
    return fetcher<{ id: number; name: string }[]>('Category/brief');
  },

  // POST: /Category
  add: async (data: CategoryData): Promise<boolean> => {
    const formData = new FormData();
    if (data.groupId !== undefined && data.groupId !== null)
      formData.append('groupId', data.groupId?.toString());
    if (data.name) formData.append('name', data.name);
    if (data.displayOrder)
      formData.append('displayOrder', data.displayOrder.toString());
    if (data.image?.[0]) formData.append('image', data.image[0]);
    if (data.isSoldByQ !== undefined)
      formData.append('isSoldByQ', data.isSoldByQ.toString());
    if (data.isInternalProduct !== undefined)
      formData.append('isInternalProduct', data.isInternalProduct.toString());
    if (data.isAvailable !== undefined)
      formData.append('isAvailable', data.isAvailable.toString());

    return fetcher<boolean>(
      'Category',
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  // PUT: /Category/{id}
  update: async (id: number, data: CategoryData): Promise<boolean> => {
    const formData = new FormData();
    if (data.groupId !== undefined && data.groupId !== null)
      formData.append('groupTypeId', data.groupId.toString());
    if (data.name) formData.append('name', data.name);
    if (data.displayOrder)
      formData.append('displayOrder', data.displayOrder.toString());
    if (data.image?.[0]) formData.append('image', data.image[0]);
    if (data.isSoldByQ !== undefined)
      formData.append('isSoldByQ', data.isSoldByQ.toString());
    if (data.isInternalProduct !== undefined)
      formData.append('isInternalProduct', data.isInternalProduct.toString());
    if (data.isAvailable !== undefined)
      formData.append('isAvailable', data.isAvailable.toString());

    return fetcher<boolean>(
      `Category/${id}`,
      {
        method: 'PUT',
        body: formData,
      },
      true,
    );
  },
  // GET: /Category/{id}/properties
  getPropertySelections: async (
    categoryId: number,
  ): Promise<CategoryProperty> => {
    return fetcher<CategoryProperty>(`Category/${categoryId}/properties`);
  },

  // POST: /Category/{id}/properties
  saveSelections: async (
    categoryId: number,
    selection1: number | null,
    selection2: number | null,
  ): Promise<boolean> => {
    const payload = {
      selection1,
      selection2,
    };

    return fetcher<boolean>(`Category/${categoryId}/properties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
  },

  // GET: /Category/{id}/PropertyDetails
  getAllPropertyDetails: async (
    categoryId: number,
  ): Promise<PropertyDetail[]> => {
    return fetcher<PropertyDetail[]>(`Category/${categoryId}/PropertyDetails`);
  },

  getAllAddOn: async (): Promise<CategoryWithOfferingsDto[]> => {
    return fetcher<CategoryWithOfferingsDto[]>(`Category/GetAllAddOn`);
  },

  // GET: /Category/{id}/PropertyDetails
  // getAllProperties: async (
  //   categoryId: number,
  // ): Promise<OfferingPropertyFormData> => {
  //   return fetcher<OfferingPropertyFormData>(
  //     `Category/${categoryId}/AllProperties`,
  //   );
  // },
};

export default CategoryQuery;

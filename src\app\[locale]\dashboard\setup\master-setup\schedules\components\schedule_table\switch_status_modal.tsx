import { useMutation } from '@tanstack/react-query';

import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { cn } from '@/lib/utils';
import ScheduleQuery from '@/services/queries/ScheduleQuery';
import { getQueryClient } from '@/utils/query-client';
import { Button } from '@/components/ui/button';

interface ISwitchStatusModal {
  close: () => void;
  isChecked: boolean;
  scheduleId: number;
}

export default function SwitchStatusModal({
  close,
  isChecked,
  scheduleId,
}: ISwitchStatusModal) {
  const { mutate, isPending } = useMutation({
    mutationKey: ScheduleQuery.tags,
    mutationFn: ScheduleQuery.toggleActive,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: ScheduleQuery.tags,
      });
      close();
    },
  });

  return (
    <ModalContainer
      onSubmit={(e) => {
        e.preventDefault();
        mutate(scheduleId);
      }}
      className="gap-0"
      title={isChecked ? 'Activate Schedule' : 'Deactivate Schedule'}
      description={
        isChecked
          ? 'This schedule will be activated and available for use.'
          : "This schedule will be deactivated and won't be available for use."
      }
      controls={
        <div className="flex flex-row gap-2">
          <Button
            disabled={isPending}
            type="submit"
            className={cn({
              'bg-green-500': isChecked,
              'bg-destructive': !isChecked,
            })}
          >
            {isPending
              ? 'Please wait...'
              : isChecked
                ? 'Activate'
                : 'Deactivate'}
          </Button>
          <Button onClick={close} variant="secondary" disabled={isPending}>
            Cancel
          </Button>
        </div>
      }
    />
  );
}

'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, Edit2, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CategoryQuery from '@/services/queries/CategoryQuery';
import { CategoryDto } from '@/models/Category';
import GroupQuery from '@/services/queries/GroupQuery';

export const CategoryTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: CategoryQuery.tags,
    queryFn: CategoryQuery.getAll,
  });

  const { data: groups, isLoading: isLoadingGroups } = useQuery({
    queryKey: GroupQuery.tags,
    queryFn: GroupQuery.getBrief,
  });

  const columns = generateTableColumns<CategoryDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      group: { name: 'Group', type: 'text', sortable: true },
      displayOrder: { name: 'Display Order', type: 'text', sortable: true },
      isAvailable: {
        name: 'Available',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Yes</span>
                </>
              ) : (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">No</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Link
                href={`/dashboard/setup/products-services/category/${row.id ?? 'add'}`}
              >
                <Button variant="secondary" size="icon">
                  <Edit2 className="size-4" />
                </Button>
              </Link>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<CategoryDto>({
    name: { name: 'Name', type: 'text' },
    code: { name: 'Code', type: 'text' },
    group: {
      name: 'Group',
      type: {
        type: 'select',
        options:
          (groups &&
            groups.map((o) => ({
              label: o.name,
              value: o.name,
            }))) ||
          [],
      },
    },
    isAvailable: {
      name: 'Available',
      type: {
        type: 'select',
        options: [
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href={`/dashboard/setup/products-services/category/add`}>
            <Button variant="main">
              <FaPlus />
              Add New Category
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default CategoryTable;
